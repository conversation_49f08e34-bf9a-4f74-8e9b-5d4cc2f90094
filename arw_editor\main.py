#!/usr/bin/env python3
"""
Sony ARW RAW Photo Editor
Main application entry point
"""

import sys
import os
from pathlib import Path
from typing import Optional

# Add the current directory to Python path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from PySide6.QtWidgets import QApplication, QMessageBox
    from PySide6.QtCore import Qt, QDir
    from PySide6.QtGui import QIcon
except ImportError as e:
    print(f"Error importing PySide6: {e}")
    print("Please install PySide6: pip install PySide6")
    sys.exit(1)

try:
    import rawpy
except ImportError as e:
    print(f"Error importing rawpy: {e}")
    print("Please install rawpy: pip install rawpy")
    sys.exit(1)

from src.main_window import MainWindow
from src.settings_manager import SettingsManager


class ARWEditorApp:
    """Main application class for the Sony ARW RAW Photo Editor"""
    
    def __init__(self):
        self.app: Optional[QApplication] = None
        self.main_window: Optional[MainWindow] = None
        self.settings_manager: Optional[SettingsManager] = None
    
    def initialize(self) -> bool:
        """Initialize the application"""
        try:
            # Create QApplication
            self.app = QApplication(sys.argv)
            self.app.setApplicationName("Sony ARW RAW Editor")
            self.app.setApplicationVersion("1.0.0")
            self.app.setOrganizationName("ARW Editor")

            # Set application properties
            self.app.setAttribute(Qt.AA_DontShowIconsInMenus, False)

            # Initialize settings manager
            self.settings_manager = SettingsManager()

            # Create main window
            self.main_window = MainWindow(self.settings_manager)

            # Set application icon if available
            icon_path = current_dir / "assets" / "icon.png"
            if icon_path.exists():
                self.app.setWindowIcon(QIcon(str(icon_path)))

            # Load stylesheet
            stylesheet_path = current_dir / "assets" / "styles.qss"
            if stylesheet_path.exists():
                with open(stylesheet_path, 'r') as f:
                    self.app.setStyleSheet(f.read())

            return True

        except Exception as e:
            self._show_error(f"Failed to initialize application: {e}")
            return False
    
    def run(self) -> int:
        """Run the application"""
        if not self.initialize():
            return 1

        try:
            # Show main window
            self.main_window.show()

            # Start event loop
            return self.app.exec()

        except Exception as e:
            self._show_error(f"Application error: {e}")
            return 1
    
    def _show_error(self, message: str) -> None:
        """Show error message to user"""
        if self.app:
            QMessageBox.critical(None, "ARW Editor Error", message)
        else:
            print(f"Error: {message}")


def main() -> int:
    """Main entry point"""
    try:
        # Check Python version
        if sys.version_info < (3, 8):
            print("Error: Python 3.8 or higher is required")
            return 1

        # Create and run application
        app = ARWEditorApp()
        return app.run()

    except Exception as e:
        print(f"Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
