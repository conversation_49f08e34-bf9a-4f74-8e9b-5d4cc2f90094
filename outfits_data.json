{"laser_cannon": {"id": "laser_cannon", "name": "Laser Cannon", "category": "weapons", "subcategory": "energy", "cost": 1350, "space_required": 2, "min_tech_level": 1, "outfitter_icon": "C:/Users/<USER>/Documents/Code/EscapeVelocityPy/assets/images/outfits/weapons/basiclaser.png", "outfitter_image": "C:/Users/<USER>/Documents/Code/EscapeVelocityPy/assets/images/outfits/weapons/basiclaser.png", "description": "Basic energy weapon. Reliable and efficient.", "mount_type": "fixed", "fire_rate": 3.0, "range": 400, "energy_usage": 8.0, "uses_ammo": false, "ammo_type": "", "max_ammo": 0, "fire_sound": "lasercannon.mp3", "shield_damage": 10, "armor_damage": 10, "projectile_behavior": "instant", "projectile_speed": 10, "tracking_strength": 0.0, "proximity_radius": 0, "delay_time": 0.0, "beam_color": [0, 0, 255], "beam_size": [8, 2], "power_cost": 5.0}, "misslerack": {"id": "misslerack", "name": "Missile Rack", "category": "weapons", "subcategory": "launcher", "cost": 1000, "space_required": 1, "min_tech_level": 1, "outfitter_icon": "C:/Users/<USER>/Documents/Code/EscapeVelocityPy/assets/images/outfits/weapons/misslelauncher.png", "outfitter_image": "C:/Users/<USER>/Documents/Code/EscapeVelocityPy/assets/images/outfits/weapons/misslelauncher.png", "description": "Basic missile launcher. Requires missile ammunition. Compatible with various missile types.", "mount_type": "fixed", "fire_rate": 1.0, "range": 600, "energy_usage": 1.0, "uses_ammo": true, "ammo_type": "light_missile", "max_ammo": 15, "fire_sound": "", "power_cost": 5.0}, "basicshieldbooster": {"id": "basicshieldbooster", "name": "Basic Shield Booster", "category": "defense", "subcategory": "shields", "cost": 1500, "space_required": 1, "min_tech_level": 1, "outfitter_icon": "src/assets/images/outfitters/defense/shield_booster_icon.png", "outfitter_image": "src/assets/images/outfitters/defense/shield_booster_large.png", "description": "Basic shield generator. Increases shield capacity and recharge rate.", "shield_boost": 100, "armor_boost": 0, "shield_recharge_boost": 1.0, "damage_reduction": 0.0}, "torpedolauncher": {"id": "torpedolauncher", "name": "Torpedo Launcher", "category": "weapons", "subcategory": "launcher", "cost": 1000, "space_required": 1, "min_tech_level": 1, "outfitter_icon": "C:/Users/<USER>/Documents/Code/EscapeVelocityPy/assets/images/outfits/weapons/torpedolauncher.png", "outfitter_image": "C:/Users/<USER>/Documents/Code/EscapeVelocityPy/assets/images/outfits/weapons/torpedolauncher.png", "description": "Heavy torpedo launcher for capital ship combat.", "mount_type": "fixed", "fire_rate": 5.0, "range": 300, "energy_usage": 0.0, "uses_ammo": true, "ammo_type": "torpedo", "max_ammo": 8, "fire_sound": "", "power_cost": 50.0}, "Light Laser Turret": {"id": "Light Laser Turret", "name": "Light Laser Turret", "category": "weapons", "subcategory": "energy", "cost": 1000, "space_required": 1, "min_tech_level": 1, "outfitter_icon": "C:/Users/<USER>/Documents/Code/EscapeVelocityPy/assets/images/outfits/weapons/autolaser.png", "outfitter_image": "C:/Users/<USER>/Documents/Code/EscapeVelocityPy/assets/images/outfits/weapons/autolaser.png", "description": "Automated light laser turret with independent targeting.", "mount_type": "turret", "fire_rate": 5.0, "range": 300, "energy_usage": 1.0, "uses_ammo": false, "ammo_type": "", "max_ammo": 0, "fire_sound": "", "shield_damage": 1, "armor_damage": 1, "projectile_behavior": "instant", "projectile_speed": 10, "tracking_strength": 0.0, "proximity_radius": 0, "delay_time": 0.0, "beam_color": [255, 255, 0], "beam_size": [8, 2], "power_cost": 5.0}, "Gatling Laser": {"id": "G<PERSON><PERSON> Laser", "name": "G<PERSON><PERSON> Laser", "category": "weapons", "subcategory": "energy", "cost": 1000, "space_required": 1, "min_tech_level": 1, "outfitter_icon": "", "outfitter_image": "", "description": "", "mount_type": "fixed", "fire_rate": 10.0, "range": 400, "energy_usage": 1.0, "uses_ammo": false, "ammo_type": "", "max_ammo": 0, "fire_sound": "", "shield_damage": 2, "armor_damage": 2, "projectile_behavior": "instant", "projectile_speed": 800, "tracking_strength": 0.0, "proximity_radius": 0, "delay_time": 0.0, "beam_color": [255, 0, 255], "beam_size": [4, 2], "power_cost": 2.0}, "Space Bomb Rack": {"id": "Space Bomb Rack", "name": "Space Bomb Rack", "category": "weapons", "subcategory": "energy", "cost": 1000, "space_required": 1, "min_tech_level": 1, "outfitter_icon": "C:/Users/<USER>/Documents/Code/EscapeVelocityPy/assets/images/outfits/weapons/bomblauncher.png", "outfitter_image": "C:/Users/<USER>/Documents/Code/EscapeVelocityPy/assets/images/outfits/weapons/bomblauncher.png", "description": "", "mount_type": "fixed", "fire_rate": 1.0, "range": 300, "energy_usage": 1.0, "uses_ammo": true, "ammo_type": "", "max_ammo": 5, "fire_sound": "", "power_cost": 1.0}, "fastgun": {"id": "fastgun", "name": "Fastgun", "category": "weapons", "subcategory": "energy", "cost": 1000, "space_required": 1, "min_tech_level": 1, "outfitter_icon": "", "outfitter_image": "", "description": "", "mount_type": "fixed", "fire_rate": 1.0, "range": 300, "energy_usage": 1.0, "uses_ammo": false, "ammo_type": "", "max_ammo": 0, "fire_sound": "", "shield_damage": 10, "armor_damage": 10, "projectile_behavior": "instant", "projectile_speed": 800, "tracking_strength": 0.0, "proximity_radius": 0, "delay_time": 0.0, "beam_color": [255, 0, 0], "beam_size": [8, 2], "power_cost": 5.0}, "Intel Missle": {"id": "Intel Missle", "name": "Intel Missle", "category": "ammunition", "subcategory": "", "cost": 500, "space_required": 1, "min_tech_level": 1, "outfitter_icon": "C:/Users/<USER>/Documents/Code/EscapeVelocityPy/assets/images/outfits/weapons/spacetorpedo.png", "outfitter_image": "C:/Users/<USER>/Documents/Code/EscapeVelocityPy/assets/images/outfits/weapons/spacetorpedo.png", "description": "", "quantity": 10, "shield_damage": 40, "armor_damage": 60, "projectile_speed": 300, "range": 600, "projectile_behavior": "guided", "tracking_strength": 1.0, "delay_time": 0.0, "explosion_radius": 20, "projectile_sprite": "C:/Users/<USER>/Documents/Code/EscapeVelocityPy/assets/images/sprites/projectiles/missle_spritesheet.png", "compatible_launchers": ["Mtester", "misslerack"], "animation_endpoints": {"trail_effect": {"effect": "engine_trail", "position": "impact_point", "duration": 0.5, "scale": 1.0, "continuous": false}, "impact_explosion": {"effect": "engine_trail", "position": "impact_point", "duration": 0.5, "scale": 1.0, "continuous": false}, "timeout_explosion": {"effect": "small_explosion", "position": "impact_point", "duration": 0.5, "scale": 1.0, "continuous": false}}}, "sdfdsf": {"id": "sdfdsf", "name": "Sdfdsf", "category": "ammunition", "subcategory": "", "cost": 500, "space_required": 1, "min_tech_level": 1, "outfitter_icon": "", "outfitter_image": "", "description": "", "quantity": 10, "shield_damage": 40, "armor_damage": 60, "projectile_speed": 300, "range": 600, "projectile_behavior": "dumbfire", "tracking_strength": 0.0, "delay_time": 0.0, "explosion_radius": 20, "projectile_sprite": "", "compatible_launchers": ["Mtester"], "animation_endpoints": {"trail_effect": {"effect": "engine_trail", "position": "impact_point", "duration": 0.5, "scale": 1.0, "continuous": false}, "impact_explosion": {"effect": "engine_trail", "position": "impact_point", "duration": 0.5, "scale": 1.0, "continuous": false}, "timeout_explosion": {"effect": "small_explosion", "position": "impact_point", "duration": 0.5, "scale": 1.0, "continuous": false}}}, "Mtester": {"id": "Mtester", "name": "Mtester", "category": "weapons", "subcategory": "energy", "cost": 1000, "space_required": 1, "min_tech_level": 1, "outfitter_icon": "", "outfitter_image": "", "description": "", "mount_type": "fixed", "fire_rate": 0.0, "range": 300, "energy_usage": 0.0, "uses_ammo": true, "ammo_type": "", "max_ammo": 0, "fire_sound": "", "power_cost": 0.0}, "tester1": {"id": "tester1", "name": "Tester1", "category": "ammunition", "subcategory": "", "cost": 500, "space_required": 1, "min_tech_level": 1, "outfitter_icon": "", "outfitter_image": "", "description": "", "quantity": 10, "shield_damage": 40, "armor_damage": 60, "projectile_speed": 300, "range": 600, "projectile_behavior": "dumbfire", "tracking_strength": 0.0, "delay_time": 0.0, "explosion_radius": 20, "projectile_sprite": "", "compatible_launchers": ["Mtester", "Space Bomb Rack", "misslerack", "torpedolauncher"], "animation_endpoints": {"trail_effect": {"effect": "engine_trail", "position": "impact_point", "duration": 0.5, "scale": 1.0, "continuous": false}, "impact_explosion": {"effect": "engine_trail", "position": "impact_point", "duration": 0.5, "scale": 1.0, "continuous": false}, "timeout_explosion": {"effect": "small_explosion", "position": "impact_point", "duration": 0.5, "scale": 1.0, "continuous": false}}}}