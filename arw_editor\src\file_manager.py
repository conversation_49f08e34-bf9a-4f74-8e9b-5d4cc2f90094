"""
File Manager for Sony ARW RAW Photo Editor
Handles file operations, browsing, and thumbnail generation
"""

import logging
import os
import shutil
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, Future
import hashlib

from PySide6.QtCore import QObject, Signal, QThread, QTimer
from PIL import Image
import numpy as np

import config
from .image_processor import ImageProcessor, ProcessingResult


@dataclass
class FileInfo:
    """Information about a file"""
    path: Path
    name: str
    size: int
    modified_time: float
    file_type: str  # 'arw', 'jpeg', 'other'
    has_pair: bool = False  # True if ARW has paired JPEG
    pair_path: Optional[Path] = None
    thumbnail_path: Optional[Path] = None
    metadata: Optional[Dict[str, Any]] = None


class ThumbnailGenerator(QThread):
    """Background thread for generating thumbnails"""
    
    thumbnail_ready = Signal(str, str)  # file_path, thumbnail_path
    thumbnail_failed = Signal(str, str)  # file_path, error_message
    
    def __init__(self, file_path: str, output_path: str, size: Tuple[int, int] = (150, 100)):
        super().__init__()
        self.file_path = file_path
        self.output_path = output_path
        self.size = size
        self.logger = logging.getLogger(__name__)
    
    def run(self):
        """Generate thumbnail in background thread"""
        try:
            file_path = Path(self.file_path)
            
            if file_path.suffix.lower() in config.SUPPORTED_RAW_EXTENSIONS:
                # Generate thumbnail from RAW file
                self._generate_raw_thumbnail()
            else:
                # Generate thumbnail from regular image file
                self._generate_image_thumbnail()
                
        except Exception as e:
            self.logger.error(f"Failed to generate thumbnail for {self.file_path}: {e}")
            self.thumbnail_failed.emit(self.file_path, str(e))
    
    def _generate_raw_thumbnail(self):
        """Generate thumbnail from RAW file"""
        processor = ImageProcessor()
        
        # Load RAW file
        result = processor.load_raw_file(self.file_path)
        if not result.success:
            self.thumbnail_failed.emit(self.file_path, result.error_message)
            return
        
        # Try to get embedded thumbnail first
        thumb_result = processor.get_embedded_thumbnail()
        if not thumb_result.success:
            # Fallback to quick processing
            thumb_result = processor.process_image(config.DEFAULT_PROCESSING_PARAMS, preview_mode=True)
        
        if thumb_result.success and thumb_result.image is not None:
            # Convert to PIL Image and resize
            if thumb_result.image.dtype != np.uint8:
                # Convert to 8-bit
                image_8bit = (thumb_result.image / thumb_result.image.max() * 255).astype(np.uint8)
            else:
                image_8bit = thumb_result.image
            
            pil_image = Image.fromarray(image_8bit)
            pil_image.thumbnail(self.size, Image.Resampling.LANCZOS)
            
            # Save thumbnail
            os.makedirs(os.path.dirname(self.output_path), exist_ok=True)
            pil_image.save(self.output_path, "JPEG", quality=85)
            
            self.thumbnail_ready.emit(self.file_path, self.output_path)
        else:
            self.thumbnail_failed.emit(self.file_path, "Failed to process RAW file")
        
        processor.close_current_file()
    
    def _generate_image_thumbnail(self):
        """Generate thumbnail from regular image file"""
        try:
            with Image.open(self.file_path) as img:
                img.thumbnail(self.size, Image.Resampling.LANCZOS)
                
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Save thumbnail
                os.makedirs(os.path.dirname(self.output_path), exist_ok=True)
                img.save(self.output_path, "JPEG", quality=85)
                
                self.thumbnail_ready.emit(self.file_path, self.output_path)
                
        except Exception as e:
            self.thumbnail_failed.emit(self.file_path, str(e))


class FileManager(QObject):
    """Manages file operations, browsing, and thumbnail generation"""
    
    # Signals
    directory_changed = Signal(str)                    # new_directory
    files_loaded = Signal(list)                       # List[FileInfo]
    thumbnail_generated = Signal(str, str)            # file_path, thumbnail_path
    file_operation_completed = Signal(str, bool, str) # operation, success, message
    
    def __init__(self, settings_manager):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        self.settings_manager = settings_manager
        
        # Current state
        self._current_directory: Optional[Path] = None
        self._current_files: List[FileInfo] = []
        self._thumbnail_cache: Dict[str, str] = {}  # file_path -> thumbnail_path
        
        # Thumbnail generation
        self._thumbnail_threads: List[ThumbnailGenerator] = []
        self._thumbnail_executor = ThreadPoolExecutor(max_workers=2)
        
        # Cache directory
        self._cache_dir = config.get_cache_dir() / "thumbnails"
        self._cache_dir.mkdir(exist_ok=True)
    
    def set_directory(self, directory: Union[str, Path]) -> bool:
        """Set the current directory and load files"""
        directory = Path(directory)
        
        if not directory.exists() or not directory.is_dir():
            self.logger.warning(f"Invalid directory: {directory}")
            return False
        
        try:
            self._current_directory = directory
            self.settings_manager.set_last_directory(str(directory))
            
            # Load files from directory
            self._load_files()
            
            self.directory_changed.emit(str(directory))
            self.logger.info(f"Directory changed to: {directory}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to set directory {directory}: {e}")
            return False
    
    def get_current_directory(self) -> Optional[Path]:
        """Get the current directory"""
        return self._current_directory
    
    def get_current_files(self) -> List[FileInfo]:
        """Get the current file list"""
        return self._current_files.copy()
    
    def refresh_files(self) -> None:
        """Refresh the current file list"""
        if self._current_directory:
            self._load_files()
    
    def get_file_info(self, file_path: Union[str, Path]) -> Optional[FileInfo]:
        """Get information about a specific file"""
        file_path = Path(file_path)
        
        for file_info in self._current_files:
            if file_info.path == file_path:
                return file_info
        
        return None
    
    def get_thumbnail_path(self, file_path: Union[str, Path]) -> Optional[str]:
        """Get thumbnail path for a file"""
        file_path_str = str(file_path)
        return self._thumbnail_cache.get(file_path_str)
    
    def generate_thumbnail(self, file_path: Union[str, Path], force: bool = False) -> None:
        """Generate thumbnail for a file"""
        file_path = Path(file_path)
        file_path_str = str(file_path)
        
        # Check if thumbnail already exists
        if not force and file_path_str in self._thumbnail_cache:
            existing_thumb = self._thumbnail_cache[file_path_str]
            if Path(existing_thumb).exists():
                return
        
        # Generate cache path
        file_hash = hashlib.md5(file_path_str.encode()).hexdigest()
        thumbnail_path = self._cache_dir / f"{file_hash}.jpg"
        
        # Create thumbnail generator thread
        generator = ThumbnailGenerator(
            file_path_str, 
            str(thumbnail_path),
            config.UI_CONFIG["thumbnail_size"]
        )
        
        # Connect signals
        generator.thumbnail_ready.connect(self._on_thumbnail_ready)
        generator.thumbnail_failed.connect(self._on_thumbnail_failed)
        generator.finished.connect(lambda: self._cleanup_thumbnail_thread(generator))
        
        # Start generation
        generator.start()
        self._thumbnail_threads.append(generator)
    
    def copy_file(self, source: Union[str, Path], destination: Union[str, Path]) -> bool:
        """Copy a file"""
        try:
            source = Path(source)
            destination = Path(destination)
            
            # Ensure destination directory exists
            destination.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.copy2(source, destination)
            
            self.file_operation_completed.emit("copy", True, f"Copied {source.name}")
            self.logger.info(f"File copied: {source} -> {destination}")
            return True
            
        except Exception as e:
            error_msg = f"Failed to copy {source}: {e}"
            self.file_operation_completed.emit("copy", False, error_msg)
            self.logger.error(error_msg)
            return False
    
    def move_file(self, source: Union[str, Path], destination: Union[str, Path]) -> bool:
        """Move a file"""
        try:
            source = Path(source)
            destination = Path(destination)
            
            # Ensure destination directory exists
            destination.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.move(source, destination)
            
            # Refresh file list if moved from current directory
            if source.parent == self._current_directory:
                self.refresh_files()
            
            self.file_operation_completed.emit("move", True, f"Moved {source.name}")
            self.logger.info(f"File moved: {source} -> {destination}")
            return True
            
        except Exception as e:
            error_msg = f"Failed to move {source}: {e}"
            self.file_operation_completed.emit("move", False, error_msg)
            self.logger.error(error_msg)
            return False
    
    def delete_file(self, file_path: Union[str, Path]) -> bool:
        """Delete a file"""
        try:
            file_path = Path(file_path)
            
            if file_path.exists():
                file_path.unlink()
                
                # Remove from current files list
                self._current_files = [f for f in self._current_files if f.path != file_path]
                
                # Remove thumbnail from cache
                file_path_str = str(file_path)
                if file_path_str in self._thumbnail_cache:
                    thumb_path = Path(self._thumbnail_cache[file_path_str])
                    if thumb_path.exists():
                        thumb_path.unlink()
                    del self._thumbnail_cache[file_path_str]
                
                self.file_operation_completed.emit("delete", True, f"Deleted {file_path.name}")
                self.files_loaded.emit(self._current_files)
                self.logger.info(f"File deleted: {file_path}")
                return True
            else:
                self.file_operation_completed.emit("delete", False, f"File not found: {file_path.name}")
                return False
                
        except Exception as e:
            error_msg = f"Failed to delete {file_path}: {e}"
            self.file_operation_completed.emit("delete", False, error_msg)
            self.logger.error(error_msg)
            return False
    
    # Private methods
    def _load_files(self) -> None:
        """Load files from the current directory"""
        if not self._current_directory:
            return
        
        try:
            files = []
            
            # Get all files in directory
            for file_path in self._current_directory.iterdir():
                if file_path.is_file():
                    file_info = self._create_file_info(file_path)
                    if file_info:
                        files.append(file_info)
            
            # Sort files by name
            files.sort(key=lambda f: f.name.lower())
            
            # Find ARW+JPEG pairs
            self._find_file_pairs(files)
            
            self._current_files = files
            self.files_loaded.emit(files)
            
            # Generate thumbnails for visible files
            self._generate_thumbnails_for_files(files[:20])  # First 20 files
            
            self.logger.info(f"Loaded {len(files)} files from {self._current_directory}")
            
        except Exception as e:
            self.logger.error(f"Failed to load files from {self._current_directory}: {e}")
    
    def _create_file_info(self, file_path: Path) -> Optional[FileInfo]:
        """Create FileInfo object for a file"""
        try:
            stat = file_path.stat()
            
            # Determine file type
            suffix = file_path.suffix.lower()
            if suffix in config.SUPPORTED_RAW_EXTENSIONS:
                file_type = 'arw'
            elif suffix in ['.jpg', '.jpeg']:
                file_type = 'jpeg'
            elif suffix in config.FILE_BROWSER_CONFIG.get("supported_preview_formats", []):
                file_type = 'other'
            else:
                return None  # Skip unsupported files
            
            return FileInfo(
                path=file_path,
                name=file_path.name,
                size=stat.st_size,
                modified_time=stat.st_mtime,
                file_type=file_type
            )
            
        except Exception as e:
            self.logger.warning(f"Failed to get info for {file_path}: {e}")
            return None
    
    def _find_file_pairs(self, files: List[FileInfo]) -> None:
        """Find ARW+JPEG pairs"""
        if not config.FILE_BROWSER_CONFIG.get("auto_pair_jpeg", True):
            return
        
        # Create lookup for JPEG files
        jpeg_files = {f.path.stem.lower(): f for f in files if f.file_type == 'jpeg'}
        
        # Find pairs for ARW files
        for file_info in files:
            if file_info.file_type == 'arw':
                stem = file_info.path.stem.lower()
                if stem in jpeg_files:
                    file_info.has_pair = True
                    file_info.pair_path = jpeg_files[stem].path
                    jpeg_files[stem].has_pair = True
                    jpeg_files[stem].pair_path = file_info.path
    
    def _generate_thumbnails_for_files(self, files: List[FileInfo]) -> None:
        """Generate thumbnails for a list of files"""
        for file_info in files:
            if file_info.file_type in ['arw', 'jpeg', 'other']:
                self.generate_thumbnail(file_info.path)
    
    def _on_thumbnail_ready(self, file_path: str, thumbnail_path: str) -> None:
        """Handle thumbnail generation completion"""
        self._thumbnail_cache[file_path] = thumbnail_path
        self.thumbnail_generated.emit(file_path, thumbnail_path)
        self.logger.debug(f"Thumbnail generated: {file_path}")
    
    def _on_thumbnail_failed(self, file_path: str, error_message: str) -> None:
        """Handle thumbnail generation failure"""
        self.logger.warning(f"Thumbnail generation failed for {file_path}: {error_message}")
    
    def _cleanup_thumbnail_thread(self, thread: ThumbnailGenerator) -> None:
        """Clean up finished thumbnail thread"""
        if thread in self._thumbnail_threads:
            self._thumbnail_threads.remove(thread)
        thread.deleteLater()
    
    def cleanup(self) -> None:
        """Clean up resources"""
        # Wait for thumbnail threads to finish
        for thread in self._thumbnail_threads:
            thread.wait(1000)  # Wait up to 1 second
            if thread.isRunning():
                thread.terminate()
        
        self._thumbnail_threads.clear()
        self._thumbnail_executor.shutdown(wait=False)
