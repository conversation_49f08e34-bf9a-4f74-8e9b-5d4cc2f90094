#!/usr/bin/env python3
"""
Installation and setup script for Sony ARW RAW Photo Editor
"""

import sys
import subprocess
import os
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    
    print(f"✓ Python version: {sys.version}")
    return True


def install_dependencies():
    """Install required dependencies"""
    print("\nInstalling dependencies...")
    
    try:
        # Upgrade pip first
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # Install requirements
        requirements_file = Path(__file__).parent / "requirements.txt"
        if requirements_file.exists():
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", str(requirements_file)])
            print("✓ Dependencies installed successfully")
            return True
        else:
            print("Error: requirements.txt not found")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"Error installing dependencies: {e}")
        return False


def check_dependencies():
    """Check if all required dependencies are available"""
    print("\nChecking dependencies...")
    
    required_packages = [
        ("rawpy", "RAW image processing"),
        ("PySide6", "GUI framework"),
        ("numpy", "Numerical computing"),
        ("PIL", "Image processing"),
        ("imageio", "Image I/O")
    ]
    
    missing_packages = []
    
    for package, description in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} - {description}")
        except ImportError:
            print(f"✗ {package} - {description} (MISSING)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\nMissing packages: {', '.join(missing_packages)}")
        return False
    
    print("✓ All dependencies are available")
    return True


def create_directories():
    """Create necessary directories"""
    print("\nCreating directories...")
    
    base_dir = Path(__file__).parent
    directories = [
        base_dir / "cache",
        base_dir / "presets",
        base_dir / "assets"
    ]
    
    for directory in directories:
        directory.mkdir(exist_ok=True)
        print(f"✓ Created: {directory}")
    
    return True


def run_tests():
    """Run basic functionality tests"""
    print("\nRunning basic tests...")
    
    try:
        test_script = Path(__file__).parent / "test_basic_functionality.py"
        if test_script.exists():
            result = subprocess.run([sys.executable, str(test_script)], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✓ Basic tests passed")
                return True
            else:
                print("✗ Some tests failed")
                print("Test output:")
                print(result.stdout)
                if result.stderr:
                    print("Errors:")
                    print(result.stderr)
                return False
        else:
            print("⚠ Test script not found, skipping tests")
            return True
            
    except Exception as e:
        print(f"Error running tests: {e}")
        return False


def launch_application():
    """Launch the main application"""
    print("\nLaunching Sony ARW RAW Photo Editor...")
    
    try:
        main_script = Path(__file__).parent / "main.py"
        if main_script.exists():
            # Launch in a new process so this script can exit
            subprocess.Popen([sys.executable, str(main_script)])
            print("✓ Application launched successfully")
            return True
        else:
            print("Error: main.py not found")
            return False
            
    except Exception as e:
        print(f"Error launching application: {e}")
        return False


def main():
    """Main installation and setup process"""
    print("Sony ARW RAW Photo Editor - Installation & Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Create directories
    if not create_directories():
        return False
    
    # Check if dependencies are already installed
    if not check_dependencies():
        print("\nSome dependencies are missing. Installing...")
        if not install_dependencies():
            return False
        
        # Check again after installation
        if not check_dependencies():
            print("Failed to install all dependencies")
            return False
    
    # Run tests
    if not run_tests():
        print("\n⚠ Some tests failed, but you can still try running the application")
    
    # Ask user if they want to launch the application
    print("\n" + "=" * 50)
    response = input("Would you like to launch the application now? (y/n): ").lower().strip()
    
    if response in ['y', 'yes']:
        if launch_application():
            print("\nSetup complete! The application should now be running.")
            print("\nTo run the application again later, use:")
            print(f"  python {Path(__file__).parent / 'main.py'}")
        else:
            print("\nSetup complete, but failed to launch application.")
            print("You can try running it manually:")
            print(f"  python {Path(__file__).parent / 'main.py'}")
    else:
        print("\nSetup complete! To run the application, use:")
        print(f"  python {Path(__file__).parent / 'main.py'}")
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\nSetup failed. Please check the error messages above.")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\nSetup interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error during setup: {e}")
        sys.exit(1)
