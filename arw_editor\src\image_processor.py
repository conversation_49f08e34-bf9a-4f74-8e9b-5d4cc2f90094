"""
Image Processor for Sony ARW RAW Photo Editor
Core RAW processing engine using rawpy
"""

import logging
import numpy as np
from pathlib import Path
from typing import Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass
from threading import Lock

import rawpy
from PIL import Image
import imageio

import config


@dataclass
class ProcessingResult:
    """Result of image processing operation"""
    success: bool
    image: Optional[np.ndarray] = None
    error_message: Optional[str] = None
    processing_time: float = 0.0
    metadata: Optional[Dict[str, Any]] = None


class ImageProcessor:
    """Core image processing engine for RAW files"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._processing_lock = Lock()
        self._current_raw: Optional[rawpy.RawPy] = None
        self._current_file_path: Optional[Path] = None
        self._cached_preview: Optional[np.ndarray] = None
        self._cached_params: Optional[Dict[str, float]] = None
    
    def load_raw_file(self, file_path: Union[str, Path]) -> ProcessingResult:
        """Load a RAW file for processing"""
        file_path = Path(file_path)
        
        if not file_path.exists():
            return ProcessingResult(
                success=False,
                error_message=f"File not found: {file_path}"
            )
        
        if file_path.suffix.lower() not in config.SUPPORTED_RAW_EXTENSIONS:
            return ProcessingResult(
                success=False,
                error_message=f"Unsupported file format: {file_path.suffix}"
            )
        
        try:
            with self._processing_lock:
                # Close previous file if open
                if self._current_raw is not None:
                    self._current_raw.close()
                
                # Load new file
                self._current_raw = rawpy.imread(str(file_path))
                self._current_file_path = file_path
                self._cached_preview = None
                self._cached_params = None
            
            # Extract metadata
            metadata = self._extract_metadata()
            
            self.logger.info(f"Successfully loaded RAW file: {file_path.name}")
            return ProcessingResult(
                success=True,
                metadata=metadata
            )
            
        except Exception as e:
            self.logger.error(f"Failed to load RAW file {file_path}: {e}")
            return ProcessingResult(
                success=False,
                error_message=str(e)
            )
    
    def process_image(self, params: Dict[str, float], preview_mode: bool = False) -> ProcessingResult:
        """Process the loaded RAW image with given parameters"""
        if self._current_raw is None:
            return ProcessingResult(
                success=False,
                error_message="No RAW file loaded"
            )

        try:
            import time
            start_time = time.time()

            # Debug logging
            self.logger.debug(f"Processing image with params: {params}")
            self.logger.debug(f"Param types: {[(k, type(v)) for k, v in params.items()]}")

            with self._processing_lock:
                # Check if we can use cached preview
                if (preview_mode and
                    self._cached_preview is not None and
                    self._cached_params == params):

                    processing_time = time.time() - start_time
                    return ProcessingResult(
                        success=True,
                        image=self._cached_preview,
                        processing_time=processing_time
                    )

                # Convert UI parameters to rawpy parameters
                rawpy_params = self._convert_params_to_rawpy(params, preview_mode)

                # Debug logging for rawpy parameters
                self.logger.debug(f"rawpy_params: {rawpy_params}")
                self.logger.debug(f"rawpy_params types: {[(k, type(v)) for k, v in rawpy_params.items()]}")

                # Process the image
                processed_image = self._current_raw.postprocess(**rawpy_params)

                # Cache preview if in preview mode
                if preview_mode:
                    self._cached_preview = processed_image
                    self._cached_params = params.copy()

            processing_time = time.time() - start_time

            self.logger.debug(f"Image processed in {processing_time:.3f}s (preview: {preview_mode})")
            return ProcessingResult(
                success=True,
                image=processed_image,
                processing_time=processing_time
            )

        except Exception as e:
            self.logger.error(f"Failed to process image: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return ProcessingResult(
                success=False,
                error_message=str(e)
            )
    
    def get_embedded_thumbnail(self) -> ProcessingResult:
        """Extract embedded thumbnail from RAW file"""
        if self._current_raw is None:
            return ProcessingResult(
                success=False,
                error_message="No RAW file loaded"
            )
        
        try:
            # Try to get embedded thumbnail
            thumb = self._current_raw.extract_thumb()
            
            if thumb.format == rawpy.ThumbFormat.JPEG:
                # Convert JPEG bytes to numpy array
                from io import BytesIO
                thumb_image = Image.open(BytesIO(thumb.data))
                thumb_array = np.array(thumb_image)
                
                return ProcessingResult(
                    success=True,
                    image=thumb_array
                )
            else:
                # Fallback to quick processing
                quick_params = self._get_quick_preview_params()
                return self.process_image(config.DEFAULT_PROCESSING_PARAMS, preview_mode=True)
                
        except Exception as e:
            self.logger.warning(f"Failed to extract thumbnail: {e}")
            # Fallback to quick processing
            return self.process_image(config.DEFAULT_PROCESSING_PARAMS, preview_mode=True)
    
    def get_auto_adjustments(self) -> Dict[str, float]:
        """Calculate automatic adjustments for the current image"""
        if self._current_raw is None:
            return config.DEFAULT_PROCESSING_PARAMS.copy()
        
        try:
            # Use camera white balance
            auto_params = config.DEFAULT_PROCESSING_PARAMS.copy()
            
            # Basic auto adjustments
            auto_params.update({
                "warmth": 0.0,  # Use camera white balance
                "tint": 0.0,
                "brightness": 0.1,  # Slight brightness boost
                "contrast": 0.05,   # Slight contrast boost
            })
            
            self.logger.debug("Auto adjustments calculated")
            return auto_params
            
        except Exception as e:
            self.logger.error(f"Failed to calculate auto adjustments: {e}")
            return config.DEFAULT_PROCESSING_PARAMS.copy()
    
    def close_current_file(self) -> None:
        """Close the currently loaded RAW file"""
        with self._processing_lock:
            if self._current_raw is not None:
                self._current_raw.close()
                self._current_raw = None
                self._current_file_path = None
                self._cached_preview = None
                self._cached_params = None
    
    def get_current_file_info(self) -> Optional[Dict[str, Any]]:
        """Get information about the currently loaded file"""
        if self._current_file_path is None:
            return None
        
        return {
            "file_path": str(self._current_file_path),
            "file_name": self._current_file_path.name,
            "file_size": self._current_file_path.stat().st_size,
            "is_loaded": self._current_raw is not None,
        }
    
    # Private methods
    def _convert_params_to_rawpy(self, params: Dict[str, float], preview_mode: bool) -> Dict[str, Any]:
        """Convert UI parameters to rawpy postprocess parameters"""
        rawpy_params = config.RAWPY_DEFAULT_PARAMS.copy()

        # Validate and sanitize input parameters using centralized validation
        validated_params = config.validate_parameters(params)

        # Map exposure (UI: -2.0 to +3.0, rawpy: 0.25 to 8.0)
        exposure_ui = validated_params.get("exposure", 0.0)
        rawpy_params["exp_shift"] = max(0.25, 2.0 ** exposure_ui)
        
        # Map highlights preservation
        rawpy_params["exp_preserve_highlights"] = max(0.0, min(1.0, validated_params.get("highlights", 0.0)))

        # Map brightness
        brightness_ui = validated_params.get("brightness", 0.0)
        rawpy_params["bright"] = 1.0 + brightness_ui

        # White balance handling
        warmth = validated_params.get("warmth", 0.0)
        tint = validated_params.get("tint", 0.0)
        
        if abs(warmth) > 0.01 or abs(tint) > 0.01:
            # Manual white balance
            rawpy_params["use_camera_wb"] = False
            rawpy_params["use_auto_wb"] = False
            # Calculate user_wb multipliers (simplified)
            base_temp = 5500  # Daylight
            temp_shift = warmth * 1000  # ±1000K
            new_temp = base_temp + temp_shift
            
            # Simplified color temperature to RGB multipliers
            if new_temp < 5500:
                r_mult = 1.0 + (5500 - new_temp) / 5500 * 0.3
                b_mult = 1.0 - (5500 - new_temp) / 5500 * 0.3
            else:
                r_mult = 1.0 - (new_temp - 5500) / 5500 * 0.3
                b_mult = 1.0 + (new_temp - 5500) / 5500 * 0.3
            
            g_mult = 1.0 + tint * 0.2  # Tint adjustment
            
            rawpy_params["user_wb"] = [r_mult, g_mult, g_mult, b_mult]
        
        # Adjust for preview mode
        if preview_mode:
            # Lower quality for faster processing
            rawpy_params["half_size"] = True
            rawpy_params["output_bps"] = 8
        else:
            # Full quality for export
            rawpy_params["half_size"] = False
            rawpy_params["output_bps"] = 16
        
        return rawpy_params



    def _extract_metadata(self) -> Dict[str, Any]:
        """Extract metadata from the current RAW file"""
        if self._current_raw is None:
            return {}
        
        try:
            metadata = {
                "camera_make": getattr(self._current_raw, 'camera_make', 'Unknown'),
                "camera_model": getattr(self._current_raw, 'camera_model', 'Unknown'),
                "iso": getattr(self._current_raw, 'iso', 0),
                "shutter": getattr(self._current_raw, 'shutter', 0),
                "aperture": getattr(self._current_raw, 'aperture', 0),
                "focal_length": getattr(self._current_raw, 'focal_length', 0),
                "timestamp": getattr(self._current_raw, 'timestamp', 0),
                "raw_width": getattr(self._current_raw, 'raw_width', 0),
                "raw_height": getattr(self._current_raw, 'raw_height', 0),
            }
            
            return metadata
            
        except Exception as e:
            self.logger.warning(f"Failed to extract metadata: {e}")
            return {}
    
    def _get_quick_preview_params(self) -> Dict[str, Any]:
        """Get parameters for quick preview generation"""
        return {
            "half_size": True,
            "output_bps": 8,
            "use_camera_wb": True,
            "no_auto_bright": True,
            "gamma": (2.222, 4.5),
        }
    
    def __del__(self):
        """Cleanup when object is destroyed"""
        self.close_current_file()
