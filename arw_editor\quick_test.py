#!/usr/bin/env python3
"""
Quick test to verify the Sony ARW RAW Photo Editor is working
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """Test that all modules can be imported"""
    print("Testing imports...")
    
    try:
        import config
        print("✓ config")
        
        from src.settings_manager import SettingsManager
        print("✓ settings_manager")
        
        from src.image_processor import ImageProcessor
        print("✓ image_processor")
        
        from src.file_manager import FileManager
        print("✓ file_manager")
        
        from src.export_manager import ExportManager
        print("✓ export_manager")
        
        from src.ui_components import ParameterSlider, ParameterGroup, StatusBar
        print("✓ ui_components")
        
        from src.main_window import MainWindow
        print("✓ main_window")
        
        return True
        
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False

def test_basic_functionality():
    """Test basic functionality without GUI"""
    print("\nTesting basic functionality...")
    
    try:
        # Test settings manager
        from src.settings_manager import SettingsManager
        settings = SettingsManager()
        
        # Test parameter setting
        settings.set_processing_param("exposure", 1.5)
        params = settings.get_processing_params()
        assert params["exposure"] == 1.5
        print("✓ Settings manager")
        
        # Test image processor (without actual file)
        from src.image_processor import ImageProcessor
        processor = ImageProcessor()
        
        # Test parameter conversion
        ui_params = {"exposure": 1.0, "brightness": 0.5}
        rawpy_params = processor._convert_params_to_rawpy(ui_params, preview_mode=True)
        assert "exp_shift" in rawpy_params
        assert "bright" in rawpy_params
        print("✓ Image processor")
        
        # Test file manager
        from src.file_manager import FileManager
        file_manager = FileManager(settings)
        print("✓ File manager")
        
        # Test export manager
        from src.export_manager import ExportManager
        export_manager = ExportManager(settings)
        print("✓ Export manager")
        
        return True
        
    except Exception as e:
        print(f"✗ Functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run quick tests"""
    print("Sony ARW RAW Photo Editor - Quick Test")
    print("=" * 40)
    
    # Test imports
    if not test_imports():
        print("\n❌ Import tests failed")
        return False
    
    # Test basic functionality
    if not test_basic_functionality():
        print("\n❌ Functionality tests failed")
        return False
    
    print("\n✅ All tests passed!")
    print("\nThe application should be ready to use.")
    print("Run 'python main.py' to start the GUI application.")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
