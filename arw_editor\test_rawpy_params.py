#!/usr/bin/env python3
"""
Test rawpy parameter handling to identify the correct format
"""

import sys
from pathlib import Path

def test_rawpy_basic():
    """Test basic rawpy functionality"""
    try:
        import rawpy
        print("✓ rawpy imported successfully")
        
        # Try to find an ARW file to test with
        test_dir = Path(r"C:\Users\<USER>\Pictures\Tests")
        arw_files = list(test_dir.glob("*.ARW")) if test_dir.exists() else []
        
        if not arw_files:
            print("No ARW files found for testing")
            return False
        
        test_file = arw_files[0]
        print(f"Testing with: {test_file}")
        
        # Load the file
        with rawpy.imread(str(test_file)) as raw:
            print("✓ RAW file loaded successfully")
            
            # Test minimal parameters
            try:
                print("Testing minimal parameters...")
                result = raw.postprocess()
                print(f"✓ Minimal processing successful: {result.shape}")
            except Exception as e:
                print(f"✗ Minimal processing failed: {e}")
                return False
            
            # Test with basic parameters
            try:
                print("Testing basic parameters...")
                result = raw.postprocess(
                    use_camera_wb=True,
                    half_size=True,
                    output_bps=8
                )
                print(f"✓ Basic processing successful: {result.shape}")
            except Exception as e:
                print(f"✗ Basic processing failed: {e}")
                return False
            
            # Test problematic parameters one by one
            problematic_params = [
                {"output_color": 1},
                {"output_color": rawpy.ColorSpace.sRGB},
                {"gamma": (2.222, 4.5)},
                {"exp_shift": 1.0},
                {"bright": 1.0},
                {"exp_preserve_highlights": 0.0},
            ]
            
            for i, param_set in enumerate(problematic_params):
                try:
                    print(f"Testing parameter set {i+1}: {param_set}")
                    result = raw.postprocess(
                        use_camera_wb=True,
                        half_size=True,
                        output_bps=8,
                        **param_set
                    )
                    print(f"✓ Parameter set {i+1} successful")
                except Exception as e:
                    print(f"✗ Parameter set {i+1} failed: {e}")
                    print(f"  Problematic parameter: {param_set}")
                    
                    # Try to identify the specific issue
                    for key, value in param_set.items():
                        try:
                            single_param = {key: value}
                            result = raw.postprocess(
                                use_camera_wb=True,
                                half_size=True,
                                output_bps=8,
                                **single_param
                            )
                            print(f"  ✓ {key}={value} works individually")
                        except Exception as e2:
                            print(f"  ✗ {key}={value} fails: {e2}")
        
        return True
        
    except ImportError:
        print("✗ rawpy not available")
        return False
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rawpy_enums():
    """Test rawpy enum values"""
    try:
        import rawpy
        
        print("\nTesting rawpy enums...")
        
        # Check available color spaces
        if hasattr(rawpy, 'ColorSpace'):
            print("Available ColorSpace values:")
            for attr in dir(rawpy.ColorSpace):
                if not attr.startswith('_'):
                    value = getattr(rawpy.ColorSpace, attr)
                    print(f"  {attr}: {value}")
        else:
            print("ColorSpace enum not found")
        
        # Check other enums
        enum_classes = ['ColorSpace', 'DemosaicAlgorithm', 'HighlightMode']
        for enum_name in enum_classes:
            if hasattr(rawpy, enum_name):
                enum_class = getattr(rawpy, enum_name)
                print(f"\n{enum_name} values:")
                for attr in dir(enum_class):
                    if not attr.startswith('_'):
                        try:
                            value = getattr(enum_class, attr)
                            print(f"  {attr}: {value}")
                        except:
                            pass
        
        return True
        
    except Exception as e:
        print(f"Enum test failed: {e}")
        return False

def main():
    """Run rawpy parameter tests"""
    print("Sony ARW RAW Photo Editor - rawpy Parameter Tests")
    print("=" * 55)
    
    success = True
    
    if not test_rawpy_basic():
        success = False
    
    if not test_rawpy_enums():
        success = False
    
    if success:
        print("\n✅ All rawpy tests passed!")
    else:
        print("\n❌ Some rawpy tests failed")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
