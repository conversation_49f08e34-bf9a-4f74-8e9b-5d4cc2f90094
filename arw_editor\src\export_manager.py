"""
Export Manager for Sony ARW RAW Photo Editor
Handles image export to various formats with quality and size controls
"""

import logging
import os
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple, Union
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, Future
import time

from PySide6.QtCore import QObject, Signal, QThread
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox,
    QComboBox, QSpinBox, QSlider, QLabel, QPushButton, QFileDialog,
    QProgressDialog, QMessageBox, QCheckBox, QLineEdit
)
from PIL import Image, ImageEnhance
import numpy as np

import config
from .image_processor import ImageProcessor, ProcessingResult


@dataclass
class ExportSettings:
    """Export configuration settings"""
    format: str  # 'JPEG', 'PNG', 'WEBP'
    quality: int  # 1-100 for JPEG/WEBP, 0-9 for PNG compression
    resize_mode: str  # 'original', 'width', 'height', 'custom'
    target_width: int
    target_height: int
    dpi: int
    output_directory: str
    filename_template: str  # Template for output filename
    preserve_metadata: bool


class ExportThread(QThread):
    """Background thread for image export"""
    
    export_progress = Signal(int, str)  # progress_percent, status_message
    export_complete = Signal(bool, str, str)  # success, output_path, error_message
    
    def __init__(self, processor: ImageProcessor, params: Dict[str, float], 
                 export_settings: ExportSettings, source_path: str):
        super().__init__()
        self.processor = processor
        self.params = params
        self.export_settings = export_settings
        self.source_path = source_path
        self.logger = logging.getLogger(__name__)
    
    def run(self):
        """Run export in background thread"""
        try:
            self.export_progress.emit(10, "Processing RAW image...")
            
            # Process image at full quality
            result = self.processor.process_image(self.params, preview_mode=False)
            
            if not result.success:
                self.export_complete.emit(False, "", result.error_message or "Processing failed")
                return
            
            self.export_progress.emit(40, "Converting image...")
            
            # Convert to PIL Image
            image_array = result.image
            if image_array.dtype != np.uint8:
                # Convert to 8-bit
                image_array = (image_array / image_array.max() * 255).astype(np.uint8)
            
            if len(image_array.shape) == 3:
                pil_image = Image.fromarray(image_array, 'RGB')
            else:
                pil_image = Image.fromarray(image_array, 'L')
            
            self.export_progress.emit(60, "Applying resize and adjustments...")
            
            # Apply resize if needed
            pil_image = self._apply_resize(pil_image)
            
            self.export_progress.emit(80, "Saving file...")
            
            # Generate output path
            output_path = self._generate_output_path()
            
            # Save image
            self._save_image(pil_image, output_path)
            
            self.export_progress.emit(100, "Export complete")
            self.export_complete.emit(True, output_path, "")
            
        except Exception as e:
            self.logger.error(f"Export failed: {e}")
            self.export_complete.emit(False, "", str(e))
    
    def _apply_resize(self, image: Image.Image) -> Image.Image:
        """Apply resize settings to image"""
        settings = self.export_settings
        
        if settings.resize_mode == 'original':
            return image
        
        original_size = image.size
        
        if settings.resize_mode == 'width':
            # Resize to target width, maintain aspect ratio
            ratio = settings.target_width / original_size[0]
            new_height = int(original_size[1] * ratio)
            new_size = (settings.target_width, new_height)
        
        elif settings.resize_mode == 'height':
            # Resize to target height, maintain aspect ratio
            ratio = settings.target_height / original_size[1]
            new_width = int(original_size[0] * ratio)
            new_size = (new_width, settings.target_height)
        
        elif settings.resize_mode == 'custom':
            # Custom size (may not maintain aspect ratio)
            new_size = (settings.target_width, settings.target_height)
        
        else:
            return image
        
        # Resize with high quality
        return image.resize(new_size, Image.Resampling.LANCZOS)
    
    def _generate_output_path(self) -> str:
        """Generate output file path"""
        settings = self.export_settings
        source_path = Path(self.source_path)
        
        # Generate filename from template
        filename_base = source_path.stem
        
        # Replace template variables
        filename = settings.filename_template.replace("{name}", filename_base)
        filename = filename.replace("{format}", settings.format.lower())
        
        # Add timestamp if needed
        if "{timestamp}" in filename:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = filename.replace("{timestamp}", timestamp)
        
        # Add appropriate extension
        if settings.format == 'JPEG':
            extension = '.jpg'
        elif settings.format == 'PNG':
            extension = '.png'
        elif settings.format == 'WEBP':
            extension = '.webp'
        else:
            extension = '.jpg'
        
        if not filename.endswith(extension):
            filename += extension
        
        return os.path.join(settings.output_directory, filename)
    
    def _save_image(self, image: Image.Image, output_path: str):
        """Save image with format-specific settings"""
        settings = self.export_settings
        
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Format-specific save parameters
        save_kwargs = {}
        
        if settings.format == 'JPEG':
            save_kwargs.update({
                'format': 'JPEG',
                'quality': settings.quality,
                'optimize': True,
                'progressive': True,
            })
            
            # Set DPI
            if settings.dpi > 0:
                save_kwargs['dpi'] = (settings.dpi, settings.dpi)
        
        elif settings.format == 'PNG':
            save_kwargs.update({
                'format': 'PNG',
                'compress_level': settings.quality,  # 0-9 for PNG
                'optimize': True,
            })
            
            # Set DPI
            if settings.dpi > 0:
                save_kwargs['dpi'] = (settings.dpi, settings.dpi)
        
        elif settings.format == 'WEBP':
            save_kwargs.update({
                'format': 'WEBP',
                'quality': settings.quality,
                'method': 6,  # Best quality method
            })
        
        # Save the image
        image.save(output_path, **save_kwargs)


class ExportDialog(QDialog):
    """Dialog for configuring export settings"""
    
    def __init__(self, parent=None, current_settings: Optional[ExportSettings] = None):
        super().__init__(parent)
        self.setWindowTitle("Export Image")
        self.setModal(True)
        self.resize(400, 500)
        
        # Initialize settings
        if current_settings:
            self.settings = current_settings
        else:
            self.settings = ExportSettings(
                format='JPEG',
                quality=90,
                resize_mode='original',
                target_width=1920,
                target_height=1080,
                dpi=300,
                output_directory=str(Path.home()),
                filename_template="{name}_edited",
                preserve_metadata=True
            )
        
        self.setup_ui()
        self.load_settings()
    
    def setup_ui(self):
        """Set up the export dialog UI"""
        layout = QVBoxLayout(self)
        
        # Format settings
        format_group = QGroupBox("Format")
        format_layout = QFormLayout(format_group)
        
        self.format_combo = QComboBox()
        self.format_combo.addItems(['JPEG', 'PNG', 'WEBP'])
        self.format_combo.currentTextChanged.connect(self.on_format_changed)
        format_layout.addRow("Format:", self.format_combo)
        
        self.quality_slider = QSlider(Qt.Horizontal)
        self.quality_slider.setRange(1, 100)
        self.quality_slider.setValue(90)
        self.quality_label = QLabel("90")
        self.quality_slider.valueChanged.connect(
            lambda v: self.quality_label.setText(str(v))
        )
        
        quality_layout = QHBoxLayout()
        quality_layout.addWidget(self.quality_slider)
        quality_layout.addWidget(self.quality_label)
        format_layout.addRow("Quality:", quality_layout)
        
        layout.addWidget(format_group)
        
        # Size settings
        size_group = QGroupBox("Size")
        size_layout = QFormLayout(size_group)
        
        self.resize_combo = QComboBox()
        self.resize_combo.addItems(['Original', 'Resize to Width', 'Resize to Height', 'Custom Size'])
        self.resize_combo.currentTextChanged.connect(self.on_resize_mode_changed)
        size_layout.addRow("Resize:", self.resize_combo)
        
        self.width_spin = QSpinBox()
        self.width_spin.setRange(1, 10000)
        self.width_spin.setValue(1920)
        self.width_spin.setSuffix(" px")
        size_layout.addRow("Width:", self.width_spin)
        
        self.height_spin = QSpinBox()
        self.height_spin.setRange(1, 10000)
        self.height_spin.setValue(1080)
        self.height_spin.setSuffix(" px")
        size_layout.addRow("Height:", self.height_spin)
        
        self.dpi_spin = QSpinBox()
        self.dpi_spin.setRange(72, 600)
        self.dpi_spin.setValue(300)
        self.dpi_spin.setSuffix(" DPI")
        size_layout.addRow("DPI:", self.dpi_spin)
        
        layout.addWidget(size_group)
        
        # Output settings
        output_group = QGroupBox("Output")
        output_layout = QFormLayout(output_group)
        
        # Output directory
        dir_layout = QHBoxLayout()
        self.output_dir_edit = QLineEdit()
        self.browse_btn = QPushButton("Browse...")
        self.browse_btn.clicked.connect(self.browse_output_directory)
        dir_layout.addWidget(self.output_dir_edit)
        dir_layout.addWidget(self.browse_btn)
        output_layout.addRow("Directory:", dir_layout)
        
        # Filename template
        self.filename_edit = QLineEdit()
        self.filename_edit.setPlaceholderText("e.g., {name}_edited")
        output_layout.addRow("Filename:", self.filename_edit)
        
        # Template help
        help_label = QLabel("Available: {name}, {format}, {timestamp}")
        help_label.setStyleSheet("color: gray; font-size: 10px;")
        output_layout.addRow("", help_label)
        
        layout.addWidget(output_group)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        button_layout.addStretch()
        
        self.export_btn = QPushButton("Export")
        self.export_btn.clicked.connect(self.accept)
        self.export_btn.setDefault(True)
        button_layout.addWidget(self.export_btn)
        
        layout.addLayout(button_layout)
    
    def load_settings(self):
        """Load current settings into UI"""
        self.format_combo.setCurrentText(self.settings.format)
        self.quality_slider.setValue(self.settings.quality)
        
        resize_modes = ['original', 'width', 'height', 'custom']
        resize_texts = ['Original', 'Resize to Width', 'Resize to Height', 'Custom Size']
        if self.settings.resize_mode in resize_modes:
            index = resize_modes.index(self.settings.resize_mode)
            self.resize_combo.setCurrentIndex(index)
        
        self.width_spin.setValue(self.settings.target_width)
        self.height_spin.setValue(self.settings.target_height)
        self.dpi_spin.setValue(self.settings.dpi)
        self.output_dir_edit.setText(self.settings.output_directory)
        self.filename_edit.setText(self.settings.filename_template)
        
        self.on_format_changed(self.settings.format)
        self.on_resize_mode_changed(self.resize_combo.currentText())
    
    def on_format_changed(self, format_name: str):
        """Handle format change"""
        if format_name == 'PNG':
            self.quality_slider.setRange(0, 9)
            self.quality_slider.setValue(6)
            self.quality_label.setText("6 (Compression)")
        else:
            self.quality_slider.setRange(1, 100)
            self.quality_slider.setValue(90)
            self.quality_label.setText("90")
    
    def on_resize_mode_changed(self, mode_text: str):
        """Handle resize mode change"""
        enable_width = mode_text in ['Resize to Width', 'Custom Size']
        enable_height = mode_text in ['Resize to Height', 'Custom Size']
        
        self.width_spin.setEnabled(enable_width)
        self.height_spin.setEnabled(enable_height)
    
    def browse_output_directory(self):
        """Browse for output directory"""
        directory = QFileDialog.getExistingDirectory(
            self, "Select Output Directory", self.output_dir_edit.text()
        )
        if directory:
            self.output_dir_edit.setText(directory)
    
    def get_export_settings(self) -> ExportSettings:
        """Get the configured export settings"""
        resize_modes = ['original', 'width', 'height', 'custom']
        resize_mode = resize_modes[self.resize_combo.currentIndex()]
        
        return ExportSettings(
            format=self.format_combo.currentText(),
            quality=self.quality_slider.value(),
            resize_mode=resize_mode,
            target_width=self.width_spin.value(),
            target_height=self.height_spin.value(),
            dpi=self.dpi_spin.value(),
            output_directory=self.output_dir_edit.text(),
            filename_template=self.filename_edit.text(),
            preserve_metadata=True
        )


class ExportManager(QObject):
    """Manages image export operations"""
    
    export_started = Signal(str)  # file_path
    export_progress = Signal(int, str)  # progress_percent, status_message
    export_completed = Signal(bool, str, str)  # success, output_path, error_message
    
    def __init__(self, settings_manager):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        self.settings_manager = settings_manager
        self.current_export_thread: Optional[ExportThread] = None
    
    def export_image(self, processor: ImageProcessor, params: Dict[str, float], 
                    source_path: str, parent_widget=None) -> bool:
        """Export an image with user-configured settings"""
        
        # Get current export settings
        current_settings = self._get_saved_export_settings()
        
        # Show export dialog
        dialog = ExportDialog(parent_widget, current_settings)
        if dialog.exec() != QDialog.Accepted:
            return False
        
        # Get export settings from dialog
        export_settings = dialog.get_export_settings()
        
        # Save settings for next time
        self._save_export_settings(export_settings)
        
        # Validate settings
        if not self._validate_export_settings(export_settings):
            QMessageBox.warning(
                parent_widget, 
                "Export Error", 
                "Invalid export settings. Please check your configuration."
            )
            return False
        
        # Start export
        return self._start_export(processor, params, source_path, export_settings)
    
    def _start_export(self, processor: ImageProcessor, params: Dict[str, float],
                     source_path: str, export_settings: ExportSettings) -> bool:
        """Start the export process"""
        
        # Cancel any existing export
        if self.current_export_thread and self.current_export_thread.isRunning():
            self.current_export_thread.terminate()
            self.current_export_thread.wait(1000)
        
        # Create export thread
        self.current_export_thread = ExportThread(
            processor, params, export_settings, source_path
        )
        
        # Connect signals
        self.current_export_thread.export_progress.connect(self.export_progress)
        self.current_export_thread.export_complete.connect(self.export_completed)
        
        # Start export
        self.current_export_thread.start()
        self.export_started.emit(source_path)
        
        return True
    
    def _validate_export_settings(self, settings: ExportSettings) -> bool:
        """Validate export settings"""
        # Check output directory
        if not settings.output_directory or not Path(settings.output_directory).exists():
            return False
        
        # Check filename template
        if not settings.filename_template:
            return False
        
        # Check dimensions
        if settings.resize_mode in ['width', 'custom'] and settings.target_width <= 0:
            return False
        
        if settings.resize_mode in ['height', 'custom'] and settings.target_height <= 0:
            return False
        
        return True
    
    def _get_saved_export_settings(self) -> ExportSettings:
        """Get saved export settings"""
        saved_settings = self.settings_manager.get_export_settings()
        
        return ExportSettings(
            format=saved_settings.get('format', 'JPEG'),
            quality=int(saved_settings.get('jpeg_quality', 90)),
            resize_mode='original',
            target_width=int(saved_settings.get('custom_width', 1920)),
            target_height=int(saved_settings.get('custom_height', 1080)),
            dpi=int(saved_settings.get('dpi', 300)),
            output_directory=self.settings_manager.get_last_directory(),
            filename_template="{name}_edited",
            preserve_metadata=True
        )
    
    def _save_export_settings(self, settings: ExportSettings):
        """Save export settings"""
        export_settings = {
            'format': settings.format,
            'jpeg_quality': settings.quality if settings.format == 'JPEG' else 90,
            'png_compression': settings.quality if settings.format == 'PNG' else 6,
            'webp_quality': settings.quality if settings.format == 'WEBP' else 85,
            'custom_width': settings.target_width,
            'custom_height': settings.target_height,
            'dpi': settings.dpi,
        }
        
        self.settings_manager.set_export_settings(export_settings)
    
    def cancel_export(self):
        """Cancel current export operation"""
        if self.current_export_thread and self.current_export_thread.isRunning():
            self.current_export_thread.terminate()
            self.current_export_thread.wait(1000)
