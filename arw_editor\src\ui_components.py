"""
UI Components for Sony ARW RAW Photo Editor
Reusable GUI widgets and components
"""

import logging
from typing import Dict, Any, Optional, Callable, List
from pathlib import Path

from PySide6.QtWidgets import (
    QWidget, QLabel, QSlider, QSpinBox, QDoubleSpinBox, QHBoxLayout, QVBoxLayout,
    QGroupBox, QPushButton, QComboBox, QScrollArea, QFrame, QSizePolicy,
    QListWidget, QListWidgetItem, QGridLayout, QProgressBar, QTextEdit, QStatusBar
)
from PySide6.QtCore import Qt, Signal, QTimer, QSize, QThread
from PySide6.QtGui import QPixmap, QFont, QIcon, QPainter, QPen, QBrush

import config


class ParameterSlider(QWidget):
    """Custom slider widget with label and numeric display"""
    
    value_changed = Signal(float)  # Emits the actual parameter value
    
    def __init__(self, name: str, min_val: float = -1.0, max_val: float = 1.0, 
                 default_val: float = 0.0, decimals: int = 2, suffix: str = ""):
        super().__init__()
        self.name = name
        self.min_val = min_val
        self.max_val = max_val
        self.decimals = decimals
        self.suffix = suffix
        self._updating = False
        
        self.setup_ui()
        self.set_value(default_val)
    
    def setup_ui(self):
        """Set up the UI layout"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(3)
        
        # Label and value display
        header_layout = QHBoxLayout()
        
        self.label = QLabel(self.name)
        self.label.setFont(QFont("Arial", 9))
        header_layout.addWidget(self.label)
        
        header_layout.addStretch()
        
        self.value_display = QLabel("0.00")
        self.value_display.setFont(QFont("Arial", 9, QFont.Bold))
        self.value_display.setAlignment(Qt.AlignRight)
        self.value_display.setMinimumWidth(50)
        header_layout.addWidget(self.value_display)
        
        layout.addLayout(header_layout)
        
        # Slider
        self.slider = QSlider(Qt.Horizontal)
        self.slider.setRange(-100, 100)  # UI range
        self.slider.setValue(0)
        self.slider.setTickPosition(QSlider.TicksBelow)
        self.slider.setTickInterval(25)
        self.slider.valueChanged.connect(self._on_slider_changed)
        layout.addWidget(self.slider)
        
        # Reset button (small)
        self.reset_button = QPushButton("Reset")
        self.reset_button.setMaximumWidth(60)
        self.reset_button.setMaximumHeight(20)
        self.reset_button.clicked.connect(self.reset_value)
        layout.addWidget(self.reset_button, alignment=Qt.AlignCenter)
    
    def set_value(self, value: float):
        """Set the parameter value"""
        if self._updating:
            return
        
        self._updating = True
        
        # Clamp value to range
        value = max(self.min_val, min(self.max_val, value))
        
        # Convert to slider range (-100 to 100)
        if self.max_val != self.min_val:
            # Normalize to 0.0-1.0, then map to -100 to +100
            normalized = (value - self.min_val) / (self.max_val - self.min_val)
            slider_val = int(normalized * 200 - 100)
        else:
            slider_val = 0
        
        self.slider.setValue(slider_val)
        self._update_display(value)
        
        self._updating = False
    
    def get_value(self) -> float:
        """Get the current parameter value"""
        slider_val = self.slider.value()
        # Convert from slider range to parameter range
        normalized = (slider_val + 100) / 200.0  # 0.0 to 1.0
        return self.min_val + normalized * (self.max_val - self.min_val)
    
    def reset_value(self):
        """Reset to default value (middle of range)"""
        default_val = (self.min_val + self.max_val) / 2
        self.set_value(default_val)
        self.value_changed.emit(self.get_value())
    
    def _on_slider_changed(self, slider_val: int):
        """Handle slider value change"""
        if self._updating:
            return

        value = self.get_value()
        self._update_display(value)
        # Ensure we always emit a float
        self.value_changed.emit(float(value))
    
    def _update_display(self, value: float):
        """Update the value display"""
        if self.decimals == 0:
            text = f"{int(value)}{self.suffix}"
        else:
            text = f"{value:.{self.decimals}f}{self.suffix}"
        self.value_display.setText(text)


class ParameterGroup(QGroupBox):
    """Group of related parameter sliders"""
    
    parameter_changed = Signal(str, float)  # parameter_name, value
    
    def __init__(self, title: str, parameters: List[Dict[str, Any]]):
        super().__init__(title)
        self.sliders: Dict[str, ParameterSlider] = {}
        self.setup_ui(parameters)
    
    def setup_ui(self, parameters: List[Dict[str, Any]]):
        """Set up the parameter sliders"""
        layout = QVBoxLayout(self)
        layout.setSpacing(8)
        
        for param_config in parameters:
            slider = ParameterSlider(
                name=param_config["name"],
                min_val=param_config.get("min", -1.0),
                max_val=param_config.get("max", 1.0),
                default_val=param_config.get("default", 0.0),
                decimals=param_config.get("decimals", 2),
                suffix=param_config.get("suffix", "")
            )
            
            # Create a proper slot function to ensure type safety
            def create_parameter_slot(param_key: str):
                def slot(value: float):
                    # Ensure value is always a float
                    try:
                        float_value = float(value)
                        self.parameter_changed.emit(param_key, float_value)
                    except (ValueError, TypeError):
                        # Fallback to 0.0 if conversion fails
                        self.parameter_changed.emit(param_key, 0.0)
                return slot

            slider.value_changed.connect(create_parameter_slot(param_config["key"]))
            
            self.sliders[param_config["key"]] = slider
            layout.addWidget(slider)
    
    def set_parameter(self, key: str, value: float):
        """Set a parameter value"""
        if key in self.sliders:
            self.sliders[key].set_value(value)
    
    def get_parameter(self, key: str) -> float:
        """Get a parameter value"""
        if key in self.sliders:
            return self.sliders[key].get_value()
        return 0.0
    
    def get_all_parameters(self) -> Dict[str, float]:
        """Get all parameter values"""
        return {key: slider.get_value() for key, slider in self.sliders.items()}
    
    def reset_all(self):
        """Reset all parameters to default"""
        for slider in self.sliders.values():
            slider.reset_value()


class ImagePreview(QLabel):
    """Image preview widget with zoom and pan capabilities"""
    
    def __init__(self):
        super().__init__()
        self.setMinimumSize(400, 300)
        self.setAlignment(Qt.AlignCenter)
        self.setStyleSheet("border: 1px solid gray; background-color: #2b2b2b;")
        self.setText("No image loaded")
        
        # Image data
        self._original_pixmap: Optional[QPixmap] = None
        self._scaled_pixmap: Optional[QPixmap] = None
        self._zoom_factor = 1.0
        self._pan_offset = [0, 0]
        
        # Mouse interaction
        self._last_mouse_pos = None
        self._dragging = False
        
        self.setMouseTracking(True)
    
    def set_image(self, pixmap: QPixmap):
        """Set the image to display"""
        self._original_pixmap = pixmap
        self._zoom_factor = 1.0
        self._pan_offset = [0, 0]
        self._update_display()
    
    def zoom_in(self):
        """Zoom in"""
        self._zoom_factor = min(self._zoom_factor * 1.2, 5.0)
        self._update_display()
    
    def zoom_out(self):
        """Zoom out"""
        self._zoom_factor = max(self._zoom_factor / 1.2, 0.1)
        self._update_display()
    
    def zoom_fit(self):
        """Zoom to fit the widget"""
        if not self._original_pixmap:
            return
        
        widget_size = self.size()
        image_size = self._original_pixmap.size()
        
        scale_x = widget_size.width() / image_size.width()
        scale_y = widget_size.height() / image_size.height()
        
        self._zoom_factor = min(scale_x, scale_y) * 0.95  # 95% to leave some margin
        self._pan_offset = [0, 0]
        self._update_display()
    
    def zoom_100(self):
        """Zoom to 100%"""
        self._zoom_factor = 1.0
        self._pan_offset = [0, 0]
        self._update_display()
    
    def _update_display(self):
        """Update the displayed image"""
        if not self._original_pixmap:
            return
        
        # Scale the image
        scaled_size = self._original_pixmap.size() * self._zoom_factor
        self._scaled_pixmap = self._original_pixmap.scaled(
            scaled_size, Qt.KeepAspectRatio, Qt.SmoothTransformation
        )
        
        # Apply pan offset and display
        self.setPixmap(self._scaled_pixmap)
    
    def mousePressEvent(self, event):
        """Handle mouse press for panning"""
        if event.button() == Qt.LeftButton:
            self._last_mouse_pos = event.position()
            self._dragging = True
    
    def mouseMoveEvent(self, event):
        """Handle mouse move for panning"""
        if self._dragging and self._last_mouse_pos:
            delta = event.position() - self._last_mouse_pos
            self._pan_offset[0] += delta.x()
            self._pan_offset[1] += delta.y()
            self._last_mouse_pos = event.position()
            self._update_display()
    
    def mouseReleaseEvent(self, event):
        """Handle mouse release"""
        if event.button() == Qt.LeftButton:
            self._dragging = False
            self._last_mouse_pos = None
    
    def wheelEvent(self, event):
        """Handle mouse wheel for zooming"""
        if event.angleDelta().y() > 0:
            self.zoom_in()
        else:
            self.zoom_out()


class FileThumbnail(QWidget):
    """Thumbnail widget for file browser"""
    
    clicked = Signal(str)  # file_path
    
    def __init__(self, file_path: str, file_name: str, file_type: str):
        super().__init__()
        self.file_path = file_path
        self.file_name = file_name
        self.file_type = file_type
        self.thumbnail_path: Optional[str] = None
        
        self.setFixedSize(160, 120)
        self.setStyleSheet("""
            FileThumbnail {
                border: 2px solid transparent;
                border-radius: 4px;
                background-color: #3b3b3b;
            }
            FileThumbnail:hover {
                border-color: #5a5a5a;
            }
        """)
        
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the thumbnail UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(4, 4, 4, 4)
        layout.setSpacing(2)
        
        # Thumbnail image
        self.image_label = QLabel()
        self.image_label.setFixedSize(150, 100)
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setStyleSheet("border: 1px solid #555; background-color: #2b2b2b;")
        self.image_label.setText("Loading...")
        layout.addWidget(self.image_label)
        
        # File name
        self.name_label = QLabel(self.file_name)
        self.name_label.setFont(QFont("Arial", 8))
        self.name_label.setAlignment(Qt.AlignCenter)
        self.name_label.setWordWrap(True)
        self.name_label.setMaximumHeight(16)
        layout.addWidget(self.name_label)
    
    def set_thumbnail(self, thumbnail_path: str):
        """Set the thumbnail image"""
        self.thumbnail_path = thumbnail_path
        
        try:
            pixmap = QPixmap(thumbnail_path)
            if not pixmap.isNull():
                # Scale to fit the label
                scaled_pixmap = pixmap.scaled(
                    self.image_label.size(), 
                    Qt.KeepAspectRatio, 
                    Qt.SmoothTransformation
                )
                self.image_label.setPixmap(scaled_pixmap)
            else:
                self.image_label.setText("Error")
        except Exception:
            self.image_label.setText("Error")
    
    def mousePressEvent(self, event):
        """Handle mouse click"""
        if event.button() == Qt.LeftButton:
            self.clicked.emit(self.file_path)


class StatusBar(QStatusBar):
    """Custom status bar with progress indication"""

    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the status bar UI"""
        # Status message (permanent widget on the left)
        self.status_label = QLabel("Ready")
        self.addWidget(self.status_label, 1)  # Stretch factor 1

        # Progress bar (temporary widget)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        self.addWidget(self.progress_bar)

        # File info (permanent widget on the right)
        self.file_info_label = QLabel("")
        self.addPermanentWidget(self.file_info_label)
    
    def set_status(self, message: str):
        """Set status message"""
        self.status_label.setText(message)
    
    def show_progress(self, message: str = "Processing..."):
        """Show progress bar"""
        self.status_label.setText(message)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate
    
    def hide_progress(self):
        """Hide progress bar"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("Ready")
    
    def set_file_info(self, info: str):
        """Set file information"""
        self.file_info_label.setText(info)


class BeforeAfterWidget(QWidget):
    """Widget for before/after comparison"""
    
    def __init__(self):
        super().__init__()
        self.before_image: Optional[QPixmap] = None
        self.after_image: Optional[QPixmap] = None
        self.show_before = False
        
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the before/after UI"""
        layout = QVBoxLayout(self)
        
        # Toggle button
        self.toggle_button = QPushButton("Show Original")
        self.toggle_button.setCheckable(True)
        self.toggle_button.clicked.connect(self.toggle_view)
        layout.addWidget(self.toggle_button)
        
        # Image display
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setStyleSheet("border: 1px solid gray;")
        layout.addWidget(self.image_label)
    
    def set_before_image(self, pixmap: QPixmap):
        """Set the before (original) image"""
        self.before_image = pixmap
        if self.show_before:
            self.image_label.setPixmap(pixmap)
    
    def set_after_image(self, pixmap: QPixmap):
        """Set the after (processed) image"""
        self.after_image = pixmap
        if not self.show_before:
            self.image_label.setPixmap(pixmap)
    
    def toggle_view(self):
        """Toggle between before and after"""
        self.show_before = self.toggle_button.isChecked()
        
        if self.show_before:
            self.toggle_button.setText("Show Processed")
            if self.before_image:
                self.image_label.setPixmap(self.before_image)
        else:
            self.toggle_button.setText("Show Original")
            if self.after_image:
                self.image_label.setPixmap(self.after_image)
