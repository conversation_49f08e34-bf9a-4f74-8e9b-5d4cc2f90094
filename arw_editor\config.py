"""
Configuration settings for Sony ARW RAW Photo Editor
"""

from pathlib import Path
from typing import Dict, Any, List

# Application configuration
APP_NAME = "Sony ARW RAW Editor"
APP_VERSION = "1.0.0"
APP_AUTHOR = "ARW Editor Team"

# File paths
BASE_DIR = Path(__file__).parent
ASSETS_DIR = BASE_DIR / "assets"
CACHE_DIR = BASE_DIR / "cache"
PRESETS_DIR = BASE_DIR / "presets"

# Supported file formats
SUPPORTED_RAW_EXTENSIONS = [".arw", ".ARW"]
SUPPORTED_EXPORT_FORMATS = ["JPEG", "PNG", "WEBP"]

# Image processing defaults
DEFAULT_PROCESSING_PARAMS = {
    "exposure": 0.0,          # -2.0 to +3.0 stops
    "highlights": 0.0,        # 0.0 to 1.0 (recovery)
    "shadows": 0.0,           # -1.0 to +1.0 (lift)
    "brightness": 0.0,        # -1.0 to +1.0
    "contrast": 0.0,          # -1.0 to +1.0
    "warmth": 0.0,            # -1.0 to +1.0 (white balance)
    "tint": 0.0,              # -1.0 to +1.0 (white balance)
    "saturation": 0.0,        # -1.0 to +1.0
    "sharpness": 0.0,         # 0.0 to 2.0
}

# Parameter validation specifications
PARAMETER_SPECS = {
    "exposure": {"min": -2.0, "max": 3.0, "default": 0.0, "type": float},
    "highlights": {"min": 0.0, "max": 1.0, "default": 0.0, "type": float},
    "shadows": {"min": -1.0, "max": 1.0, "default": 0.0, "type": float},
    "brightness": {"min": -1.0, "max": 1.0, "default": 0.0, "type": float},
    "contrast": {"min": -1.0, "max": 1.0, "default": 0.0, "type": float},
    "warmth": {"min": -1.0, "max": 1.0, "default": 0.0, "type": float},
    "tint": {"min": -1.0, "max": 1.0, "default": 0.0, "type": float},
    "saturation": {"min": -1.0, "max": 1.0, "default": 0.0, "type": float},
    "sharpness": {"min": 0.0, "max": 2.0, "default": 0.0, "type": float},
}

# Import rawpy for enum values
try:
    import rawpy
    RAWPY_AVAILABLE = True
except ImportError:
    RAWPY_AVAILABLE = False

# rawpy processing default parameters (valid rawpy parameters only)
if RAWPY_AVAILABLE:
    RAWPY_DEFAULT_PARAMS = {
        "use_camera_wb": True,
        "use_auto_wb": False,
        "output_color": rawpy.ColorSpace.sRGB,
        "output_bps": 8,
        "gamma": (2.222, 4.5),
        "no_auto_bright": False,
        "auto_bright_thr": 0.01,
    }
else:
    # Fallback if rawpy not available
    RAWPY_DEFAULT_PARAMS = {
        "use_camera_wb": True,
        "use_auto_wb": False,
        "output_bps": 8,
        "gamma": (2.222, 4.5),
        "no_auto_bright": False,
        "auto_bright_thr": 0.01,
    }

# UI configuration
UI_CONFIG = {
    "window_size": (1400, 1000),  # Made taller (was 900)
    "min_window_size": (1000, 700),  # Made taller (was 600)
    "preview_size": (800, 600),
    "thumbnail_size": (150, 100),
    "slider_range": (-100, 100),  # UI slider range (mapped to actual values)
    "update_delay_ms": 100,       # Delay for real-time preview updates
}

# Export settings
EXPORT_DEFAULTS = {
    "jpeg_quality": 90,
    "png_compression": 6,
    "webp_quality": 85,
    "resize_options": ["Original", "1920px", "1080px", "Custom"],
    "dpi": 300,
}

# Performance settings
PERFORMANCE_CONFIG = {
    "preview_max_size": (1024, 768),  # Max size for real-time preview
    "thumbnail_cache_size": 100,       # Number of thumbnails to cache
    "processing_threads": 2,           # Number of background processing threads
    "lazy_loading": True,              # Enable lazy loading of images
}

# File browser settings
FILE_BROWSER_CONFIG = {
    "show_hidden_files": False,
    "auto_pair_jpeg": True,           # Automatically pair ARW with JPEG
    "thumbnail_generation": True,
    "supported_preview_formats": [".jpg", ".jpeg", ".png", ".tiff"],
}

# Error handling
ERROR_CONFIG = {
    "show_detailed_errors": False,
    "log_errors": True,
    "fallback_processing": True,
}

def get_cache_dir() -> Path:
    """Get cache directory, create if it doesn't exist"""
    CACHE_DIR.mkdir(exist_ok=True)
    return CACHE_DIR

def get_presets_dir() -> Path:
    """Get presets directory, create if it doesn't exist"""
    PRESETS_DIR.mkdir(exist_ok=True)
    return PRESETS_DIR

def get_assets_dir() -> Path:
    """Get assets directory"""
    return ASSETS_DIR

def validate_parameter(name: str, value: Any) -> float:
    """Validate and sanitize a single parameter value"""
    if name not in PARAMETER_SPECS:
        raise ValueError(f"Unknown parameter: {name}")

    spec = PARAMETER_SPECS[name]

    try:
        # Handle different value types
        if hasattr(value, 'value'):
            # Handle objects with .value attribute
            float_value = float(value.value)
        elif isinstance(value, (int, float)):
            float_value = float(value)
        elif isinstance(value, str):
            float_value = float(value)
        else:
            raise ValueError(f"Unsupported value type: {type(value)}")

        # Clamp to valid range
        return max(spec["min"], min(spec["max"], float_value))

    except (ValueError, TypeError, AttributeError):
        # Return default value if conversion fails
        return spec["default"]

def validate_parameters(params: Dict[str, Any]) -> Dict[str, float]:
    """Validate and sanitize a dictionary of parameters"""
    validated = {}

    for name, value in params.items():
        if name in PARAMETER_SPECS:
            validated[name] = validate_parameter(name, value)
        # Ignore unknown parameters

    # Ensure all required parameters are present
    for name, spec in PARAMETER_SPECS.items():
        if name not in validated:
            validated[name] = spec["default"]

    return validated
