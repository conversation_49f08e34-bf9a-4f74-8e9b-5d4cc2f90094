# Sony ARW RAW Photo Editor

A clean, modular desktop application for editing Sony A7 IV ARW (RAW) files. Bridges the gap between basic OS photo viewers and complex professional software.

## Features

### Core RAW Processing
- **Exposure Control**: -2 to +3 stops adjustment
- **Highlight Recovery**: Preserve detail in bright areas
- **Shadow Lifting**: Brighten dark areas without losing detail
- **White Balance**: Warmth/tint controls for color temperature
- **Brightness & Contrast**: Overall image tone adjustments
- **Auto Settings**: One-click camera white balance + auto brightness

### File Management
- **Folder Browser**: Navigate ARW files with thumbnail previews
- **ARW+JPEG Pairing**: Compare RAW with camera JPEG
- **Before/After Toggle**: Quick comparison of original vs edited
- **File Operations**: Copy, move, delete files
- **Settings Presets**: Save and load adjustment configurations

### Export Options
- **Multiple Formats**: JPEG (web), PNG (lossless), WEBP (optimization)
- **Quality Controls**: Adjustable compression for each format
- **Size Options**: Full resolution, web-sized (1920px), custom dimensions
- **Print Ready**: 300 DPI exports for photo printing
- **Batch Processing**: Apply settings to multiple images

## Technical Stack

- **rawpy**: Sony ARW processing (LibRaw wrapper)
- **PySide6**: Modern GUI framework
- **numpy**: Image data manipulation
- **PIL/Pillow**: Output format handling
- **imageio**: Additional format support

## Architecture

Modular MVC design with separated concerns:
- `image_processor.py`: RAW processing logic
- `file_manager.py`: File operations and browsing
- `ui_components.py`: Reusable GUI widgets
- `main_window.py`: Primary application window
- `export_manager.py`: Format conversion and output
- `settings_manager.py`: User preferences and presets

## Installation

```bash
pip install -r requirements.txt
```

## Usage

```bash
python main.py
```

## Requirements

- Python 3.8+
- Sony ARW files (A7 series cameras)
- Windows/macOS/Linux desktop environment

## Philosophy

**Simplicity First**: Clean interface that doesn't overwhelm
**RAW Advantages**: Leverage superior dynamic range and color depth
**Modular Design**: Well-separated components for easy maintenance
**Personal Workflow**: Optimized for individual use, not enterprise features

## Known Limitations

- Some newer Sony ARW formats may not be supported (firmware-dependent)
- No local adjustment tools (masks, graduated filters)
- Limited noise reduction options
- No lens correction database
- Single-user application (no collaboration features)

## License

MIT License - See LICENSE file for details
