#!/usr/bin/env python3
"""
Basic functionality tests for Sony ARW RAW Photo Editor
"""

import sys
import os
import tempfile
import unittest
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Test imports
try:
    from src.settings_manager import SettingsManager, ProcessingPreset
    from src.image_processor import ImageProcessor, ProcessingResult
    from src.file_manager import FileManager, FileInfo
    from src.export_manager import ExportManager, ExportSettings
    import config
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure all dependencies are installed: pip install -r requirements.txt")
    sys.exit(1)


class TestSettingsManager(unittest.TestCase):
    """Test settings manager functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.settings_manager = SettingsManager()
    
    def test_default_parameters(self):
        """Test default processing parameters"""
        params = self.settings_manager.get_processing_params()
        
        # Check that all expected parameters are present
        expected_params = config.DEFAULT_PROCESSING_PARAMS.keys()
        for param in expected_params:
            self.assertIn(param, params)
        
        # Check default values
        self.assertEqual(params["exposure"], 0.0)
        self.assertEqual(params["brightness"], 0.0)
        self.assertEqual(params["contrast"], 0.0)
    
    def test_parameter_setting(self):
        """Test setting individual parameters"""
        self.settings_manager.set_processing_param("exposure", 1.5)
        params = self.settings_manager.get_processing_params()
        self.assertEqual(params["exposure"], 1.5)
    
    def test_parameter_reset(self):
        """Test resetting parameters"""
        self.settings_manager.set_processing_param("exposure", 2.0)
        self.settings_manager.reset_processing_params()
        params = self.settings_manager.get_processing_params()
        self.assertEqual(params["exposure"], 0.0)
    
    def test_preset_save_load(self):
        """Test saving and loading presets"""
        # Set some parameters
        test_params = {"exposure": 1.0, "brightness": 0.5, "contrast": 0.3}
        self.settings_manager.set_processing_params(test_params)
        
        # Save preset
        success = self.settings_manager.save_preset("test_preset", "Test description")
        self.assertTrue(success)
        
        # Reset parameters
        self.settings_manager.reset_processing_params()
        
        # Load preset
        success = self.settings_manager.load_preset("test_preset")
        self.assertTrue(success)
        
        # Check parameters were restored
        loaded_params = self.settings_manager.get_processing_params()
        for key, value in test_params.items():
            self.assertEqual(loaded_params[key], value)
    
    def test_preset_list(self):
        """Test getting preset list"""
        # Initially should be empty
        presets = self.settings_manager.get_preset_names()
        initial_count = len(presets)
        
        # Save a preset
        self.settings_manager.save_preset("test_preset")
        
        # Should have one more preset
        presets = self.settings_manager.get_preset_names()
        self.assertEqual(len(presets), initial_count + 1)
        self.assertIn("test_preset", presets)


class TestImageProcessor(unittest.TestCase):
    """Test image processor functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.processor = ImageProcessor()
    
    def test_parameter_conversion(self):
        """Test UI parameter to rawpy parameter conversion"""
        # Test with mock rawpy object
        with patch('rawpy.imread') as mock_imread:
            mock_raw = Mock()
            mock_imread.return_value = mock_raw
            
            # Test parameter conversion
            ui_params = {
                "exposure": 1.0,
                "highlights": 0.5,
                "brightness": 0.2,
                "warmth": 0.0,
                "tint": 0.0
            }
            
            rawpy_params = self.processor._convert_params_to_rawpy(ui_params, preview_mode=True)
            
            # Check exposure conversion
            self.assertAlmostEqual(rawpy_params["exp_shift"], 2.0, places=2)  # 2^1.0 = 2.0
            
            # Check highlights
            self.assertEqual(rawpy_params["exp_preserve_highlights"], 0.5)
            
            # Check brightness
            self.assertEqual(rawpy_params["bright"], 1.2)  # 1.0 + 0.2
            
            # Check preview mode settings
            self.assertTrue(rawpy_params["half_size"])
            self.assertEqual(rawpy_params["output_bps"], 8)
    
    def test_auto_adjustments(self):
        """Test automatic adjustment calculation"""
        auto_params = self.processor.get_auto_adjustments()
        
        # Should return valid parameters
        self.assertIsInstance(auto_params, dict)
        
        # Should contain expected keys
        expected_keys = config.DEFAULT_PROCESSING_PARAMS.keys()
        for key in expected_keys:
            self.assertIn(key, auto_params)
    
    def test_file_info(self):
        """Test file info retrieval"""
        # Without loaded file
        info = self.processor.get_current_file_info()
        self.assertIsNone(info)
        
        # Test would require actual ARW file for full testing


class TestFileManager(unittest.TestCase):
    """Test file manager functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.settings_manager = Mock()
        self.file_manager = FileManager(self.settings_manager)
        self.temp_dir = tempfile.mkdtemp()
    
    def test_directory_setting(self):
        """Test setting directory"""
        # Test with valid directory
        success = self.file_manager.set_directory(self.temp_dir)
        self.assertTrue(success)
        self.assertEqual(self.file_manager.get_current_directory(), Path(self.temp_dir))
        
        # Test with invalid directory
        success = self.file_manager.set_directory("/nonexistent/directory")
        self.assertFalse(success)
    
    def test_file_info_creation(self):
        """Test file info creation"""
        # Create a test file
        test_file = Path(self.temp_dir) / "test.txt"
        test_file.write_text("test content")
        
        file_info = self.file_manager._create_file_info(test_file)
        
        # Should return None for unsupported file type
        self.assertIsNone(file_info)
        
        # Test with supported extension (mock)
        arw_file = Path(self.temp_dir) / "test.arw"
        arw_file.write_text("fake arw content")
        
        file_info = self.file_manager._create_file_info(arw_file)
        self.assertIsNotNone(file_info)
        self.assertEqual(file_info.name, "test.arw")
        self.assertEqual(file_info.file_type, "arw")


class TestExportManager(unittest.TestCase):
    """Test export manager functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.settings_manager = Mock()
        self.export_manager = ExportManager(self.settings_manager)
    
    def test_export_settings_validation(self):
        """Test export settings validation"""
        # Valid settings
        valid_settings = ExportSettings(
            format="JPEG",
            quality=90,
            resize_mode="original",
            target_width=1920,
            target_height=1080,
            dpi=300,
            output_directory=tempfile.gettempdir(),
            filename_template="{name}_edited",
            preserve_metadata=True
        )
        
        self.assertTrue(self.export_manager._validate_export_settings(valid_settings))
        
        # Invalid settings - no output directory
        invalid_settings = ExportSettings(
            format="JPEG",
            quality=90,
            resize_mode="original",
            target_width=1920,
            target_height=1080,
            dpi=300,
            output_directory="",
            filename_template="{name}_edited",
            preserve_metadata=True
        )
        
        self.assertFalse(self.export_manager._validate_export_settings(invalid_settings))
    
    def test_saved_settings_retrieval(self):
        """Test getting saved export settings"""
        # Mock settings manager return values
        self.settings_manager.get_export_settings.return_value = {
            'format': 'PNG',
            'jpeg_quality': 95,
            'custom_width': 2048,
            'custom_height': 1536,
            'dpi': 300
        }
        self.settings_manager.get_last_directory.return_value = tempfile.gettempdir()
        
        settings = self.export_manager._get_saved_export_settings()
        
        self.assertEqual(settings.format, 'PNG')
        self.assertEqual(settings.target_width, 2048)
        self.assertEqual(settings.target_height, 1536)


class TestConfiguration(unittest.TestCase):
    """Test configuration settings"""
    
    def test_config_values(self):
        """Test that configuration values are reasonable"""
        # Check UI config
        self.assertIsInstance(config.UI_CONFIG["window_size"], tuple)
        self.assertEqual(len(config.UI_CONFIG["window_size"]), 2)
        self.assertGreater(config.UI_CONFIG["window_size"][0], 0)
        self.assertGreater(config.UI_CONFIG["window_size"][1], 0)
        
        # Check default parameters
        self.assertIsInstance(config.DEFAULT_PROCESSING_PARAMS, dict)
        self.assertIn("exposure", config.DEFAULT_PROCESSING_PARAMS)
        self.assertIn("brightness", config.DEFAULT_PROCESSING_PARAMS)
        
        # Check supported formats
        self.assertIsInstance(config.SUPPORTED_RAW_EXTENSIONS, list)
        self.assertIn(".arw", config.SUPPORTED_RAW_EXTENSIONS)
        
        # Check export formats
        self.assertIsInstance(config.SUPPORTED_EXPORT_FORMATS, list)
        self.assertIn("JPEG", config.SUPPORTED_EXPORT_FORMATS)
    
    def test_directory_creation(self):
        """Test directory creation functions"""
        cache_dir = config.get_cache_dir()
        self.assertTrue(cache_dir.exists())
        self.assertTrue(cache_dir.is_dir())
        
        presets_dir = config.get_presets_dir()
        self.assertTrue(presets_dir.exists())
        self.assertTrue(presets_dir.is_dir())


def run_tests():
    """Run all tests"""
    print("Running Sony ARW RAW Photo Editor Tests...")
    print("=" * 50)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_cases = [
        TestSettingsManager,
        TestImageProcessor,
        TestFileManager,
        TestExportManager,
        TestConfiguration
    ]
    
    for test_case in test_cases:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_case)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 50)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"  {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"  {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    print(f"\nOverall result: {'PASS' if success else 'FAIL'}")
    
    return success


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
