"""
Settings Manager for Sony ARW RAW Photo Editor
Handles user preferences, presets, and application configuration
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict

from PySide6.QtCore import QSettings, QObject, Signal

import config


@dataclass
class ProcessingPreset:
    """Data class for processing parameter presets"""
    name: str
    description: str
    parameters: Dict[str, float]
    created_date: str
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProcessingPreset':
        return cls(**data)


class SettingsManager(QObject):
    """Manages application settings, user preferences, and presets"""
    
    # Signals
    settings_changed = Signal(str, object)  # setting_name, new_value
    preset_added = Signal(str)              # preset_name
    preset_removed = Signal(str)            # preset_name
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        
        # Qt settings for persistent storage
        self.qt_settings = QSettings(config.APP_NAME, config.APP_VERSION)
        
        # Presets storage
        self.presets_file = config.get_presets_dir() / "presets.json"
        self._presets: Dict[str, ProcessingPreset] = {}
        
        # Current processing parameters
        self._current_params = config.DEFAULT_PROCESSING_PARAMS.copy()
        
        # Load settings and presets
        self._load_presets()
        self._load_ui_settings()
    
    # Processing Parameters
    def get_processing_params(self) -> Dict[str, float]:
        """Get current processing parameters"""
        return self._current_params.copy()
    
    def set_processing_param(self, param_name: str, value: float) -> None:
        """Set a single processing parameter"""
        if param_name in self._current_params:
            old_value = self._current_params[param_name]
            self._current_params[param_name] = value
            
            if old_value != value:
                self.settings_changed.emit(param_name, value)
                self.logger.debug(f"Parameter {param_name} changed: {old_value} -> {value}")
    
    def set_processing_params(self, params: Dict[str, float]) -> None:
        """Set multiple processing parameters"""
        for param_name, value in params.items():
            if param_name in self._current_params:
                self._current_params[param_name] = value
        
        self.settings_changed.emit("batch_update", params)
    
    def reset_processing_params(self) -> None:
        """Reset processing parameters to defaults"""
        self._current_params = config.DEFAULT_PROCESSING_PARAMS.copy()
        self.settings_changed.emit("reset", self._current_params)
    
    # Presets Management
    def save_preset(self, name: str, description: str = "") -> bool:
        """Save current parameters as a preset"""
        try:
            from datetime import datetime
            
            preset = ProcessingPreset(
                name=name,
                description=description,
                parameters=self._current_params.copy(),
                created_date=datetime.now().isoformat()
            )
            
            self._presets[name] = preset
            self._save_presets()
            self.preset_added.emit(name)
            
            self.logger.info(f"Preset '{name}' saved successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save preset '{name}': {e}")
            return False
    
    def load_preset(self, name: str) -> bool:
        """Load a preset and apply its parameters"""
        if name not in self._presets:
            self.logger.warning(f"Preset '{name}' not found")
            return False
        
        try:
            preset = self._presets[name]
            self.set_processing_params(preset.parameters)
            self.logger.info(f"Preset '{name}' loaded successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load preset '{name}': {e}")
            return False
    
    def delete_preset(self, name: str) -> bool:
        """Delete a preset"""
        if name not in self._presets:
            return False
        
        try:
            del self._presets[name]
            self._save_presets()
            self.preset_removed.emit(name)
            self.logger.info(f"Preset '{name}' deleted")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to delete preset '{name}': {e}")
            return False
    
    def get_preset_names(self) -> List[str]:
        """Get list of available preset names"""
        return list(self._presets.keys())
    
    def get_preset(self, name: str) -> Optional[ProcessingPreset]:
        """Get a specific preset"""
        return self._presets.get(name)
    
    # UI Settings
    def get_ui_setting(self, key: str, default: Any = None) -> Any:
        """Get a UI setting value"""
        return self.qt_settings.value(f"ui/{key}", default)
    
    def set_ui_setting(self, key: str, value: Any) -> None:
        """Set a UI setting value"""
        self.qt_settings.setValue(f"ui/{key}", value)
        self.settings_changed.emit(f"ui/{key}", value)
    
    def get_window_geometry(self) -> Optional[bytes]:
        """Get saved window geometry"""
        return self.qt_settings.value("window/geometry")
    
    def set_window_geometry(self, geometry: bytes) -> None:
        """Save window geometry"""
        self.qt_settings.setValue("window/geometry", geometry)
    
    def get_window_state(self) -> Optional[bytes]:
        """Get saved window state"""
        return self.qt_settings.value("window/state")
    
    def set_window_state(self, state: bytes) -> None:
        """Save window state"""
        self.qt_settings.setValue("window/state", state)
    
    # File and Export Settings
    def get_last_directory(self) -> str:
        """Get last used directory"""
        return self.qt_settings.value("files/last_directory", str(Path.home()))
    
    def set_last_directory(self, directory: str) -> None:
        """Set last used directory"""
        self.qt_settings.setValue("files/last_directory", directory)
    
    def get_export_settings(self) -> Dict[str, Any]:
        """Get export settings"""
        defaults = config.EXPORT_DEFAULTS
        return {
            "jpeg_quality": self.qt_settings.value("export/jpeg_quality", defaults["jpeg_quality"]),
            "png_compression": self.qt_settings.value("export/png_compression", defaults["png_compression"]),
            "webp_quality": self.qt_settings.value("export/webp_quality", defaults["webp_quality"]),
            "resize_option": self.qt_settings.value("export/resize_option", defaults["resize_options"][0]),
            "custom_width": self.qt_settings.value("export/custom_width", 1920),
            "custom_height": self.qt_settings.value("export/custom_height", 1080),
            "dpi": self.qt_settings.value("export/dpi", defaults["dpi"]),
        }
    
    def set_export_settings(self, settings: Dict[str, Any]) -> None:
        """Set export settings"""
        for key, value in settings.items():
            self.qt_settings.setValue(f"export/{key}", value)
    
    # Private methods
    def _load_presets(self) -> None:
        """Load presets from file"""
        if not self.presets_file.exists():
            return
        
        try:
            with open(self.presets_file, 'r') as f:
                data = json.load(f)
            
            self._presets = {
                name: ProcessingPreset.from_dict(preset_data)
                for name, preset_data in data.items()
            }
            
            self.logger.info(f"Loaded {len(self._presets)} presets")
            
        except Exception as e:
            self.logger.error(f"Failed to load presets: {e}")
            self._presets = {}
    
    def _save_presets(self) -> None:
        """Save presets to file"""
        try:
            config.get_presets_dir().mkdir(exist_ok=True)
            
            data = {
                name: preset.to_dict()
                for name, preset in self._presets.items()
            }
            
            with open(self.presets_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            self.logger.debug("Presets saved successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to save presets: {e}")
    
    def _load_ui_settings(self) -> None:
        """Load UI settings and apply defaults if needed"""
        # Set default values if not already set
        defaults = {
            "show_thumbnails": True,
            "auto_preview": True,
            "preview_quality": "medium",
            "theme": "system",
        }
        
        for key, default_value in defaults.items():
            if self.get_ui_setting(key) is None:
                self.set_ui_setting(key, default_value)
