# Sony ARW RAW Photo Editor - Usage Guide

## Quick Start

### Installation
1. Ensure Python 3.8+ is installed
2. Run the installation script:
   ```bash
   python install_and_run.py
   ```
3. The script will install dependencies and optionally launch the application

### Manual Installation
```bash
pip install -r requirements.txt
python main.py
```

## Interface Overview

### Main Window Layout
- **Left Panel**: File browser and adjustment controls
- **Right Panel**: Image preview with zoom controls
- **Menu Bar**: File operations, presets, and view options
- **Toolbar**: Quick access to common functions
- **Status Bar**: Progress indication and file information

### File Browser
- **Open Folder**: Browse to a directory containing ARW files
- **Thumbnails**: Preview images before opening
- **ARW+JPEG Pairing**: Automatically pairs RAW files with camera JPEGs
- **File Operations**: Copy, move, delete files (right-click context menu)

## Editing Controls

### Exposure & Tone
- **Exposure**: Adjust overall brightness (-2 to +3 stops)
- **Highlights**: Recover detail in bright areas (0 to 1.0)
- **Shadows**: Lift detail in dark areas (-1 to +1)
- **Brightness**: Fine-tune overall brightness (-1 to +1)

### Color & Contrast
- **Warmth**: Adjust color temperature (-1 to +1)
- **Tint**: Adjust green/magenta balance (-1 to +1)
- **Contrast**: Adjust image contrast (-1 to +1)
- **Saturation**: Adjust color intensity (-1 to +1)

### Quick Actions
- **Auto**: Apply automatic adjustments based on image analysis
- **Reset**: Reset all parameters to default values
- **Show Original**: Toggle between processed and original image

## Working with Images

### Loading Images
1. Click "Open Folder" or use Ctrl+O
2. Navigate to a folder containing ARW files
3. Click on a thumbnail to load the image
4. The image will appear in the preview panel

### Making Adjustments
1. Use the sliders in the left panel to adjust parameters
2. Changes are applied in real-time to the preview
3. Use the "Show Original" button to compare before/after
4. Fine-tune adjustments as needed

### Zoom and Navigation
- **Fit**: Zoom to fit the entire image in the preview
- **100%**: View image at actual size
- **Mouse Wheel**: Zoom in/out
- **Click and Drag**: Pan around when zoomed in

## Presets

### Saving Presets
1. Adjust image parameters to your liking
2. Go to Edit → Save Preset or press Ctrl+S
3. Enter a name for your preset
4. Click OK to save

### Loading Presets
1. Go to Edit → Load Preset or press Ctrl+L
2. Select a preset from the list
3. Click OK to apply the preset to the current image

### Managing Presets
- Presets are saved in the `presets/` directory
- You can manually delete preset files if needed
- Presets include all adjustment parameters

## Exporting Images

### Export Process
1. Load and adjust an ARW image
2. Go to File → Export Image or press Ctrl+E
3. Configure export settings in the dialog
4. Click Export to save the processed image

### Export Settings

#### Format Options
- **JPEG**: Best for web and general use (quality 1-100)
- **PNG**: Lossless compression (compression 0-9)
- **WEBP**: Modern web format with good compression

#### Size Options
- **Original**: Keep original image dimensions
- **Resize to Width**: Specify target width, maintain aspect ratio
- **Resize to Height**: Specify target height, maintain aspect ratio
- **Custom Size**: Specify both width and height

#### Output Settings
- **Directory**: Choose where to save exported images
- **Filename Template**: Customize output filename
  - `{name}`: Original filename without extension
  - `{format}`: Export format (jpeg, png, webp)
  - `{timestamp}`: Current date and time
- **DPI**: Set resolution for print (72-600 DPI)

### Batch Processing
Currently, batch processing requires manually loading and exporting each image. Future versions may include automated batch processing.

## Keyboard Shortcuts

### File Operations
- **Ctrl+O**: Open folder
- **Ctrl+E**: Export image
- **Ctrl+Q**: Quit application

### Editing
- **Ctrl+A**: Auto adjust
- **Ctrl+R**: Reset all parameters
- **Ctrl+S**: Save preset
- **Ctrl+L**: Load preset

### View
- **Ctrl+0**: Zoom to fit
- **Ctrl+1**: Zoom to 100%

## Tips and Best Practices

### Getting the Best Results
1. **Start with Auto**: Use the Auto button as a starting point
2. **Adjust Exposure First**: Get the overall brightness right
3. **Recover Highlights**: Use the Highlights slider to recover blown highlights
4. **Lift Shadows**: Brighten dark areas without affecting highlights
5. **Fine-tune Color**: Adjust warmth and tint for natural colors
6. **Add Contrast**: Increase contrast for more punch

### Performance Tips
1. **Preview Mode**: Real-time preview uses reduced resolution for speed
2. **Export Quality**: Full resolution processing happens only during export
3. **Thumbnail Cache**: Thumbnails are cached for faster browsing
4. **Memory Usage**: Close unused images to free memory

### File Management
1. **Organize by Date**: Keep ARW files organized by shooting date
2. **Backup Originals**: Always keep backup copies of original ARW files
3. **Export Organization**: Use descriptive filenames for exported images
4. **Preset Library**: Build a library of presets for different shooting conditions

## Troubleshooting

### Common Issues

#### Application Won't Start
- Check Python version (3.8+ required)
- Install missing dependencies: `pip install -r requirements.txt`
- Check error messages in terminal

#### Can't Load ARW Files
- Ensure files are Sony ARW format
- Check file permissions
- Try with different ARW files to isolate the issue

#### Slow Performance
- Close other applications to free memory
- Use smaller preview sizes
- Check available disk space for cache

#### Export Fails
- Check output directory permissions
- Ensure sufficient disk space
- Verify export settings are valid

### Getting Help
1. Check the error messages in the status bar
2. Look at the terminal/console for detailed error information
3. Try the basic functionality tests: `python test_basic_functionality.py`
4. Check the GitHub repository for known issues and updates

## Technical Notes

### Supported Formats
- **Input**: Sony ARW files (A7 series cameras primarily)
- **Output**: JPEG, PNG, WEBP
- **Preview**: Embedded JPEG thumbnails when available

### Processing Pipeline
1. RAW file loading using rawpy (LibRaw)
2. Parameter application in linear color space
3. Color space conversion (sRGB output)
4. Gamma correction and tone mapping
5. Format-specific compression and output

### File Structure
```
arw_editor/
├── main.py              # Application entry point
├── src/                 # Source code modules
├── assets/              # Stylesheets and resources
├── cache/               # Thumbnail cache
├── presets/             # Saved presets
├── requirements.txt     # Python dependencies
└── README.md           # Project documentation
```

### Dependencies
- **rawpy**: RAW image processing
- **PySide6**: GUI framework
- **numpy**: Numerical operations
- **Pillow**: Image manipulation
- **imageio**: Additional format support

## Future Enhancements

Potential features for future versions:
- Local adjustments (masks, gradients)
- Noise reduction controls
- Lens correction database
- Batch processing automation
- Additional RAW format support
- Color grading tools
- Histogram display
- EXIF data viewer
