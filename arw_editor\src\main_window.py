"""
Main Window for Sony ARW RAW Photo Editor
Primary application window with image preview and controls
"""

import logging
from pathlib import Path
from typing import Dict, Any, Optional
import numpy as np

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QHBoxLayout, QVBoxLayout, QSplitter, QGridLayout,
    QMenuBar, QMenu, QToolBar, QPushButton, QFileDialog, QMessageBox,
    QScrollArea, QFrame, QGroupBox, QLabel, QComboBox, QSpinBox,
    QApplication, QProgressDialog, QInputDialog, QListWidget, QDialog
)
from PySide6.QtCore import Qt, QTimer, Signal, QThread, QSize
from PySide6.QtGui import QAction, QKeySequence, QPixmap, QIcon
from PIL import Image

import config
from .settings_manager import SettingsManager
from .image_processor import ImageProcessor, ProcessingResult
from .file_manager import FileManager
from .export_manager import ExportManager
from .ui_components import (
    ParameterGroup, ImagePreview, FileThumbnail, StatusBar, BeforeAfterWidget
)


class ProcessingThread(QThread):
    """Background thread for image processing"""
    
    processing_complete = Signal(object)  # ProcessingResult
    
    def __init__(self, processor: ImageProcessor, params: Dict[str, float], preview_mode: bool = False):
        super().__init__()
        self.processor = processor
        self.params = params
        self.preview_mode = preview_mode
    
    def run(self):
        """Run processing in background"""
        result = self.processor.process_image(self.params, self.preview_mode)
        self.processing_complete.emit(result)


class MainWindow(QMainWindow):
    """Main application window"""
    
    def __init__(self, settings_manager: SettingsManager):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        self.settings_manager = settings_manager
        
        # Core components
        self.image_processor = ImageProcessor()
        self.file_manager = FileManager(settings_manager)
        self.export_manager = ExportManager(settings_manager)
        
        # UI state
        self.current_file_path: Optional[Path] = None
        self.current_params = config.DEFAULT_PROCESSING_PARAMS.copy()
        self.processing_thread: Optional[ProcessingThread] = None
        
        # Update timer for real-time preview
        self.update_timer = QTimer()
        self.update_timer.setSingleShot(True)
        self.update_timer.timeout.connect(self._update_preview)
        
        # Set up UI
        self.setup_ui()
        self.setup_connections()
        self.restore_window_state()
        
        # Load last directory
        last_dir = self.settings_manager.get_last_directory()
        if Path(last_dir).exists():
            self.file_manager.set_directory(last_dir)
    
    def setup_ui(self):
        """Set up the user interface"""
        self.setWindowTitle(f"{config.APP_NAME} v{config.APP_VERSION}")
        self.setMinimumSize(*config.UI_CONFIG["min_window_size"])
        self.resize(*config.UI_CONFIG["window_size"])
        
        # Create menu bar
        self.create_menu_bar()
        
        # Create toolbar
        self.create_toolbar()
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # Create main splitter
        main_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(main_splitter)
        
        # Left panel (file browser and controls)
        left_panel = self.create_left_panel()
        main_splitter.addWidget(left_panel)
        
        # Right panel (image preview)
        right_panel = self.create_right_panel()
        main_splitter.addWidget(right_panel)
        
        # Set splitter proportions and make it user-resizable
        main_splitter.setSizes([525, 875])  # Initial sizes (left panel larger)
        main_splitter.setChildrenCollapsible(False)  # Prevent panels from collapsing completely
        
        # Set minimum sizes so panels don't get too small
        left_panel.setMinimumWidth(300)
        right_panel.setMinimumWidth(400)
        
        # Store splitter reference for state saving
        self.main_splitter = main_splitter
        
        # Status bar
        self.status_bar = StatusBar()
        self.setStatusBar(self.status_bar)
    
    def create_menu_bar(self):
        """Create the menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("&File")
        
        open_action = QAction("&Open Folder...", self)
        open_action.setShortcut(QKeySequence.Open)
        open_action.triggered.connect(self.open_folder)
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        export_action = QAction("&Export Image...", self)
        export_action.setShortcut(QKeySequence("Ctrl+E"))
        export_action.triggered.connect(self.export_image)
        export_action.setEnabled(False)
        file_menu.addAction(export_action)
        self.export_action = export_action
        
        file_menu.addSeparator()
        
        exit_action = QAction("E&xit", self)
        exit_action.setShortcut(QKeySequence.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Edit menu
        edit_menu = menubar.addMenu("&Edit")
        
        reset_action = QAction("&Reset All", self)
        reset_action.setShortcut(QKeySequence("Ctrl+R"))
        reset_action.triggered.connect(self.reset_all_parameters)
        edit_menu.addAction(reset_action)
        
        auto_action = QAction("&Auto Adjust", self)
        auto_action.setShortcut(QKeySequence("Ctrl+A"))
        auto_action.triggered.connect(self.auto_adjust)
        edit_menu.addAction(auto_action)

        edit_menu.addSeparator()

        save_preset_action = QAction("&Save Preset...", self)
        save_preset_action.setShortcut(QKeySequence("Ctrl+S"))
        save_preset_action.triggered.connect(self.save_preset)
        edit_menu.addAction(save_preset_action)

        load_preset_action = QAction("&Load Preset...", self)
        load_preset_action.setShortcut(QKeySequence("Ctrl+L"))
        load_preset_action.triggered.connect(self.load_preset)
        edit_menu.addAction(load_preset_action)
        
        # View menu
        view_menu = menubar.addMenu("&View")
        
        zoom_fit_action = QAction("Zoom to &Fit", self)
        zoom_fit_action.setShortcut(QKeySequence("Ctrl+0"))
        zoom_fit_action.triggered.connect(lambda: self.image_preview.zoom_fit())
        view_menu.addAction(zoom_fit_action)
        
        zoom_100_action = QAction("Zoom &100%", self)
        zoom_100_action.setShortcut(QKeySequence("Ctrl+1"))
        zoom_100_action.triggered.connect(lambda: self.image_preview.zoom_100())
        view_menu.addAction(zoom_100_action)
    
    def create_toolbar(self):
        """Create the toolbar"""
        toolbar = self.addToolBar("Main")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        
        # Open folder
        open_action = QAction("Open Folder", self)
        open_action.triggered.connect(self.open_folder)
        toolbar.addAction(open_action)
        
        toolbar.addSeparator()
        
        # Auto adjust
        auto_action = QAction("Auto", self)
        auto_action.triggered.connect(self.auto_adjust)
        toolbar.addAction(auto_action)
        
        # Reset
        reset_action = QAction("Reset", self)
        reset_action.triggered.connect(self.reset_all_parameters)
        toolbar.addAction(reset_action)
        
        toolbar.addSeparator()
        
        # Export
        export_action = QAction("Export", self)
        export_action.triggered.connect(self.export_image)
        export_action.setEnabled(False)
        toolbar.addAction(export_action)
        self.toolbar_export_action = export_action
    
    def create_left_panel(self) -> QWidget:
        """Create the left panel with file browser and controls"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Create vertical splitter for resizable file browser
        left_splitter = QSplitter(Qt.Vertical)
        layout.addWidget(left_splitter)
        
        # File browser section (top part - resizable)
        file_group = QGroupBox("Files")
        file_layout = QVBoxLayout(file_group)
        
        # Directory controls
        dir_layout = QHBoxLayout()
        self.open_folder_btn = QPushButton("Open Folder...")
        self.open_folder_btn.clicked.connect(self.open_folder)
        dir_layout.addWidget(self.open_folder_btn)
        
        self.refresh_btn = QPushButton("Refresh")
        self.refresh_btn.clicked.connect(self.refresh_files)
        dir_layout.addWidget(self.refresh_btn)
        
        file_layout.addLayout(dir_layout)
        
        # File list (thumbnails) - use grid layout for better space utilization
        self.file_scroll = QScrollArea()
        self.file_scroll.setWidgetResizable(True)
        self.file_scroll.setMinimumHeight(150)  # Reduced minimum, now user can resize
        self.file_widget = QWidget()
        self.file_layout = QGridLayout(self.file_widget)
        self.file_layout.setSpacing(5)
        self.file_scroll.setWidget(self.file_widget)
        file_layout.addWidget(self.file_scroll)
        
        # Add file browser to splitter
        left_splitter.addWidget(file_group)
        
        # Processing controls section (bottom part - resizable)
        controls_panel = QWidget()
        controls_main_layout = QVBoxLayout(controls_panel)
        controls_main_layout.setContentsMargins(5, 5, 5, 5)
        
        # Processing controls
        self.create_processing_controls(controls_main_layout)
        
        # Add controls to splitter
        left_splitter.addWidget(controls_panel)
        
        # Set initial splitter proportions (file browser gets more space)
        left_splitter.setSizes([400, 300])  # File browser larger than controls
        left_splitter.setChildrenCollapsible(False)
        
        # Set minimum sizes
        file_group.setMinimumHeight(200)
        controls_panel.setMinimumHeight(250)  # Need space for all parameter sliders
        
        # Store splitter reference for state saving
        self.left_splitter = left_splitter
        
        return panel
    
    def create_processing_controls(self, parent_layout: QVBoxLayout):
        """Create processing parameter controls"""
        # Exposure group
        exposure_params = [
            {"name": "Exposure", "key": "exposure", "min": -2.0, "max": 3.0, "default": 0.0, "suffix": " EV"},
            {"name": "Highlights", "key": "highlights", "min": 0.0, "max": 1.0, "default": 0.0},
            {"name": "Shadows", "key": "shadows", "min": -1.0, "max": 1.0, "default": 0.0},
            {"name": "Brightness", "key": "brightness", "min": -1.0, "max": 1.0, "default": 0.0},
        ]
        
        self.exposure_group = ParameterGroup("Exposure & Tone", exposure_params)
        self.exposure_group.parameter_changed.connect(self.on_parameter_changed)
        parent_layout.addWidget(self.exposure_group)
        
        # Color group
        color_params = [
            {"name": "Warmth", "key": "warmth", "min": -1.0, "max": 1.0, "default": 0.0},
            {"name": "Tint", "key": "tint", "min": -1.0, "max": 1.0, "default": 0.0},
            {"name": "Contrast", "key": "contrast", "min": -1.0, "max": 1.0, "default": 0.0},
            {"name": "Saturation", "key": "saturation", "min": -1.0, "max": 1.0, "default": 0.0},
        ]
        
        self.color_group = ParameterGroup("Color & Contrast", color_params)
        self.color_group.parameter_changed.connect(self.on_parameter_changed)
        parent_layout.addWidget(self.color_group)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.auto_btn = QPushButton("Auto")
        self.auto_btn.clicked.connect(self.auto_adjust)
        button_layout.addWidget(self.auto_btn)
        
        self.reset_btn = QPushButton("Reset")
        self.reset_btn.clicked.connect(self.reset_all_parameters)
        button_layout.addWidget(self.reset_btn)
        
        parent_layout.addLayout(button_layout)
        
        parent_layout.addStretch()
    
    def create_right_panel(self) -> QWidget:
        """Create the right panel with image preview"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Create vertical splitter to make preview resizable
        preview_splitter = QSplitter(Qt.Vertical)
        layout.addWidget(preview_splitter)
        
        # Image preview (top section)
        self.image_preview = ImagePreview()
        preview_splitter.addWidget(self.image_preview)
        
        # Controls section (bottom section)
        controls_widget = QWidget()
        controls_main_layout = QVBoxLayout(controls_widget)
        controls_main_layout.setContentsMargins(5, 5, 5, 5)
        
        # Preview controls
        controls_layout = QHBoxLayout()
        
        # Zoom controls
        zoom_out_btn = QPushButton("−")
        zoom_out_btn.setMaximumWidth(30)
        zoom_out_btn.clicked.connect(self.image_preview.zoom_out)
        controls_layout.addWidget(zoom_out_btn)
        
        zoom_fit_btn = QPushButton("Fit")
        zoom_fit_btn.clicked.connect(self.image_preview.zoom_fit)
        controls_layout.addWidget(zoom_fit_btn)
        
        zoom_100_btn = QPushButton("100%")
        zoom_100_btn.clicked.connect(self.image_preview.zoom_100)
        controls_layout.addWidget(zoom_100_btn)
        
        zoom_in_btn = QPushButton("+")
        zoom_in_btn.setMaximumWidth(30)
        zoom_in_btn.clicked.connect(self.image_preview.zoom_in)
        controls_layout.addWidget(zoom_in_btn)
        
        controls_layout.addStretch()
        
        # Before/after toggle
        self.before_after_btn = QPushButton("Show Original")
        self.before_after_btn.setCheckable(True)
        self.before_after_btn.clicked.connect(self.toggle_before_after)
        controls_layout.addWidget(self.before_after_btn)
        
        controls_main_layout.addLayout(controls_layout)
        
        # Add controls widget to splitter
        preview_splitter.addWidget(controls_widget)
        
        # Set initial splitter proportions (preview gets most space)
        preview_splitter.setSizes([800, 100])  # Preview much larger than controls
        preview_splitter.setChildrenCollapsible(False)
        
        # Set minimum sizes
        self.image_preview.setMinimumHeight(200)
        controls_widget.setMinimumHeight(50)
        controls_widget.setMaximumHeight(150)  # Keep controls compact
        
        # Store splitter reference for state saving
        self.preview_splitter = preview_splitter
        
        return panel
    
    def setup_connections(self):
        """Set up signal connections"""
        # File manager connections
        self.file_manager.files_loaded.connect(self.on_files_loaded)
        self.file_manager.thumbnail_generated.connect(self.on_thumbnail_generated)
        self.file_manager.file_operation_completed.connect(self.on_file_operation_completed)

        # Export manager connections
        self.export_manager.export_started.connect(self.on_export_started)
        self.export_manager.export_progress.connect(self.on_export_progress)
        self.export_manager.export_completed.connect(self.on_export_completed)

        # Settings manager connections
        self.settings_manager.settings_changed.connect(self.on_settings_changed)
    
    # Event handlers
    def on_parameter_changed(self, param_name: str, value: float):
        """Handle parameter change"""
        # Ensure value is a float, not a string
        if isinstance(value, str):
            try:
                value = float(value)
            except ValueError:
                self.logger.error(f"Invalid parameter value for {param_name}: {value}")
                return
        
        self.current_params[param_name] = value
        self.settings_manager.set_processing_param(param_name, value)
        
        # Start update timer for real-time preview
        self.update_timer.start(config.UI_CONFIG["update_delay_ms"])
    
    def on_files_loaded(self, files):
        """Handle files loaded from directory"""
        # Clear existing thumbnails
        for i in reversed(range(self.file_layout.count())):
            child = self.file_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
        
        # Add new thumbnails in grid layout (3 columns)
        columns = 3
        row = 0
        col = 0
        
        for file_info in files:
            if file_info.file_type == 'arw':
                thumbnail = FileThumbnail(
                    str(file_info.path),
                    file_info.name,
                    file_info.file_type
                )
                thumbnail.clicked.connect(self.load_image)
                self.file_layout.addWidget(thumbnail, row, col)
                
                col += 1
                if col >= columns:
                    col = 0
                    row += 1
        
        self.status_bar.set_status(f"Loaded {len(files)} files")
    
    def on_thumbnail_generated(self, file_path: str, thumbnail_path: str):
        """Handle thumbnail generation completion"""
        # Find and update the corresponding thumbnail widget
        for i in range(self.file_layout.count()):
            item = self.file_layout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                if isinstance(widget, FileThumbnail) and widget.file_path == file_path:
                    widget.set_thumbnail(thumbnail_path)
                    break
    
    def on_file_operation_completed(self, operation: str, success: bool, message: str):
        """Handle file operation completion"""
        if success:
            self.status_bar.set_status(message)
        else:
            QMessageBox.warning(self, "File Operation Error", message)
    
    def on_settings_changed(self, setting_name: str, value):
        """Handle settings change"""
        pass  # Handle any settings-related UI updates

    def on_export_started(self, file_path: str):
        """Handle export start"""
        self.status_bar.show_progress("Exporting image...")

    def on_export_progress(self, progress: int, message: str):
        """Handle export progress update"""
        self.status_bar.set_status(f"{message} ({progress}%)")

    def on_export_completed(self, success: bool, output_path: str, error_message: str):
        """Handle export completion"""
        self.status_bar.hide_progress()

        if success:
            self.status_bar.set_status(f"Exported to: {Path(output_path).name}")
            QMessageBox.information(
                self,
                "Export Complete",
                f"Image exported successfully to:\n{output_path}"
            )
        else:
            self.status_bar.set_status("Export failed")
            QMessageBox.critical(
                self,
                "Export Error",
                f"Failed to export image:\n{error_message}"
            )
    
    # Action handlers
    def open_folder(self):
        """Open folder dialog"""
        last_dir = self.settings_manager.get_last_directory()
        folder = QFileDialog.getExistingDirectory(
            self, "Select Folder with ARW Files", last_dir
        )
        
        if folder:
            self.file_manager.set_directory(folder)
    
    def refresh_files(self):
        """Refresh file list"""
        self.file_manager.refresh_files()
    
    def load_image(self, file_path: str):
        """Load an image for editing"""
        self.current_file_path = Path(file_path)
        self.status_bar.show_progress("Loading image...")
        
        # Load RAW file
        result = self.image_processor.load_raw_file(file_path)
        
        if result.success:
            self.status_bar.set_status(f"Loaded: {self.current_file_path.name}")
            self.status_bar.set_file_info(f"{self.current_file_path.name}")
            
            # Enable export action
            self.export_action.setEnabled(True)
            self.toolbar_export_action.setEnabled(True)
            
            # Update preview
            self._update_preview()
        else:
            self.status_bar.hide_progress()
            QMessageBox.critical(self, "Load Error", f"Failed to load image:\n{result.error_message}")
    
    def auto_adjust(self):
        """Apply automatic adjustments"""
        if not self.current_file_path:
            return
        
        auto_params = self.image_processor.get_auto_adjustments()
        
        # Update UI controls
        for param_name, value in auto_params.items():
            if param_name in self.current_params:
                self.current_params[param_name] = value
                
                # Update appropriate parameter group
                if param_name in ["exposure", "highlights", "shadows", "brightness"]:
                    self.exposure_group.set_parameter(param_name, value)
                elif param_name in ["warmth", "tint", "contrast", "saturation"]:
                    self.color_group.set_parameter(param_name, value)
        
        # Update preview
        self._update_preview()
    
    def reset_all_parameters(self):
        """Reset all parameters to defaults"""
        self.current_params = config.DEFAULT_PROCESSING_PARAMS.copy()
        
        # Reset UI controls
        self.exposure_group.reset_all()
        self.color_group.reset_all()
        
        # Update preview
        self._update_preview()
    
    def toggle_before_after(self):
        """Toggle between original and processed view"""
        # This would be implemented with before/after comparison
        pass
    
    def export_image(self):
        """Export the current image"""
        if not self.current_file_path:
            return

        # Use export manager to export the image
        success = self.export_manager.export_image(
            self.image_processor,
            self.current_params,
            str(self.current_file_path),
            self
        )

        if not success:
            self.status_bar.set_status("Export cancelled")

    def save_preset(self):
        """Save current settings as a preset"""
        name, ok = QInputDialog.getText(
            self,
            "Save Preset",
            "Enter preset name:",
            text="My Preset"
        )

        if ok and name.strip():
            success = self.settings_manager.save_preset(name.strip(), "User created preset")
            if success:
                self.status_bar.set_status(f"Preset '{name}' saved")
                QMessageBox.information(self, "Preset Saved", f"Preset '{name}' saved successfully.")
            else:
                QMessageBox.warning(self, "Save Error", f"Failed to save preset '{name}'.")

    def load_preset(self):
        """Load a preset"""
        preset_names = self.settings_manager.get_preset_names()

        if not preset_names:
            QMessageBox.information(self, "No Presets", "No presets available.")
            return

        name, ok = QInputDialog.getItem(
            self,
            "Load Preset",
            "Select preset to load:",
            preset_names,
            0,
            False
        )

        if ok and name:
            success = self.settings_manager.load_preset(name)
            if success:
                # Update UI with loaded parameters
                params = self.settings_manager.get_processing_params()
                self.current_params = params

                # Update parameter groups
                for param_name, value in params.items():
                    if param_name in ["exposure", "highlights", "shadows", "brightness"]:
                        self.exposure_group.set_parameter(param_name, value)
                    elif param_name in ["warmth", "tint", "contrast", "saturation"]:
                        self.color_group.set_parameter(param_name, value)

                # Update preview
                self._update_preview()

                self.status_bar.set_status(f"Preset '{name}' loaded")
            else:
                QMessageBox.warning(self, "Load Error", f"Failed to load preset '{name}'.")
    
    def _update_preview(self):
        """Update the image preview"""
        if not self.current_file_path:
            return
        
        # Cancel any existing processing
        if self.processing_thread and self.processing_thread.isRunning():
            self.processing_thread.terminate()
            self.processing_thread.wait(100)
        
        # Validate and clean parameters before processing
        cleaned_params = {}
        for key, value in self.current_params.items():
            if isinstance(value, (int, float)):
                cleaned_params[key] = float(value)
            else:
                self.logger.warning(f"Invalid parameter type for {key}: {type(value)}, using default")
                cleaned_params[key] = config.DEFAULT_PROCESSING_PARAMS.get(key, 0.0)
        
        # Start new processing thread
        self.processing_thread = ProcessingThread(
            self.image_processor, 
            cleaned_params, 
            preview_mode=True
        )
        self.processing_thread.processing_complete.connect(self._on_preview_ready)
        self.processing_thread.start()
    
    def _on_preview_ready(self, result: ProcessingResult):
        """Handle preview processing completion"""
        if result.success and result.image is not None:
            # Convert numpy array to QPixmap
            if result.image.dtype != np.uint8:
                # Convert to 8-bit
                image_8bit = (result.image / result.image.max() * 255).astype(np.uint8)
            else:
                image_8bit = result.image
            
            # Convert to PIL Image then QPixmap (without temporary file)
            if len(image_8bit.shape) == 3:
                pil_image = Image.fromarray(image_8bit, 'RGB')
            else:
                pil_image = Image.fromarray(image_8bit, 'L')
            
            # Convert PIL to QPixmap without saving to disk
            from io import BytesIO
            buffer = BytesIO()
            pil_image.save(buffer, format='PNG')
            buffer.seek(0)
            pixmap = QPixmap()
            pixmap.loadFromData(buffer.getvalue())
            
            # Update preview
            self.image_preview.set_image(pixmap)
            self.status_bar.hide_progress()
        else:
            self.status_bar.hide_progress()
            if result.error_message:
                self.status_bar.set_status(f"Preview error: {result.error_message}")
    
    def restore_window_state(self):
        """Restore window geometry and state"""
        geometry = self.settings_manager.get_window_geometry()
        if geometry:
            self.restoreGeometry(geometry)
        
        state = self.settings_manager.get_window_state()
        if state:
            self.restoreState(state)
        
        # Restore splitter state
        splitter_state = self.settings_manager.get_ui_setting("splitter_state")
        if splitter_state:
            self.main_splitter.restoreState(splitter_state)
        
        # Restore preview splitter state
        preview_splitter_state = self.settings_manager.get_ui_setting("preview_splitter_state")
        if preview_splitter_state:
            self.preview_splitter.restoreState(preview_splitter_state)
        
        # Restore left panel splitter state
        left_splitter_state = self.settings_manager.get_ui_setting("left_splitter_state")
        if left_splitter_state:
            self.left_splitter.restoreState(left_splitter_state)
    
    def closeEvent(self, event):
        """Handle window close event"""
        # Save window state
        self.settings_manager.set_window_geometry(self.saveGeometry())
        self.settings_manager.set_window_state(self.saveState())
        
        # Save splitter state
        self.settings_manager.set_ui_setting("splitter_state", self.main_splitter.saveState())
        
        # Save preview splitter state
        self.settings_manager.set_ui_setting("preview_splitter_state", self.preview_splitter.saveState())
        
        # Save left panel splitter state
        self.settings_manager.set_ui_setting("left_splitter_state", self.left_splitter.saveState())
        
        # Clean up
        self.image_processor.close_current_file()
        self.file_manager.cleanup()
        
        if self.processing_thread and self.processing_thread.isRunning():
            self.processing_thread.terminate()
            self.processing_thread.wait(1000)
        
        event.accept()
