/* Sony ARW RAW Photo Editor Stylesheet */

/* Main application styling */
QMainWindow {
    background-color: #2b2b2b;
    color: #ffffff;
}

/* Group boxes */
QGroupBox {
    font-weight: bold;
    border: 2px solid #555555;
    border-radius: 5px;
    margin-top: 1ex;
    padding-top: 10px;
    background-color: #3b3b3b;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
    color: #ffffff;
}

/* Buttons */
QPushButton {
    background-color: #4a4a4a;
    border: 1px solid #666666;
    border-radius: 4px;
    padding: 6px 12px;
    color: #ffffff;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #5a5a5a;
    border-color: #777777;
}

QPushButton:pressed {
    background-color: #3a3a3a;
    border-color: #555555;
}

QPushButton:disabled {
    background-color: #2a2a2a;
    border-color: #444444;
    color: #666666;
}

/* Sliders */
QSlider::groove:horizontal {
    border: 1px solid #555555;
    height: 8px;
    background: #3b3b3b;
    margin: 2px 0;
    border-radius: 4px;
}

QSlider::handle:horizontal {
    background: #6a6a6a;
    border: 1px solid #555555;
    width: 18px;
    margin: -2px 0;
    border-radius: 9px;
}

QSlider::handle:horizontal:hover {
    background: #7a7a7a;
}

QSlider::handle:horizontal:pressed {
    background: #5a5a5a;
}

/* Combo boxes */
QComboBox {
    background-color: #4a4a4a;
    border: 1px solid #666666;
    border-radius: 4px;
    padding: 4px 8px;
    color: #ffffff;
    min-width: 6em;
}

QComboBox:hover {
    border-color: #777777;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 15px;
    border-left-width: 1px;
    border-left-color: #666666;
    border-left-style: solid;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
}

QComboBox::down-arrow {
    image: url(down_arrow.png);
}

QComboBox QAbstractItemView {
    background-color: #4a4a4a;
    border: 1px solid #666666;
    selection-background-color: #6a6a6a;
    color: #ffffff;
}

/* Spin boxes */
QSpinBox, QDoubleSpinBox {
    background-color: #4a4a4a;
    border: 1px solid #666666;
    border-radius: 4px;
    padding: 4px;
    color: #ffffff;
}

QSpinBox:hover, QDoubleSpinBox:hover {
    border-color: #777777;
}

/* Labels */
QLabel {
    color: #ffffff;
    background-color: transparent;
}

/* Scroll areas */
QScrollArea {
    background-color: #2b2b2b;
    border: 1px solid #555555;
}

QScrollBar:vertical {
    background-color: #3b3b3b;
    width: 15px;
    border-radius: 7px;
}

QScrollBar::handle:vertical {
    background-color: #6a6a6a;
    min-height: 20px;
    border-radius: 7px;
}

QScrollBar::handle:vertical:hover {
    background-color: #7a7a7a;
}

/* Menu bar */
QMenuBar {
    background-color: #3b3b3b;
    color: #ffffff;
    border-bottom: 1px solid #555555;
}

QMenuBar::item {
    background-color: transparent;
    padding: 4px 8px;
}

QMenuBar::item:selected {
    background-color: #5a5a5a;
}

QMenu {
    background-color: #3b3b3b;
    color: #ffffff;
    border: 1px solid #555555;
}

QMenu::item {
    padding: 4px 20px;
}

QMenu::item:selected {
    background-color: #5a5a5a;
}

/* Toolbar */
QToolBar {
    background-color: #3b3b3b;
    border: 1px solid #555555;
    spacing: 3px;
}

QToolBar::handle {
    background-color: #555555;
}

/* Status bar */
QStatusBar {
    background-color: #3b3b3b;
    color: #ffffff;
    border-top: 1px solid #555555;
}

/* Progress bar */
QProgressBar {
    border: 1px solid #555555;
    border-radius: 4px;
    text-align: center;
    background-color: #2b2b2b;
    color: #ffffff;
}

QProgressBar::chunk {
    background-color: #4a90e2;
    border-radius: 3px;
}

/* Splitter */
QSplitter::handle {
    background-color: #555555;
}

QSplitter::handle:horizontal {
    width: 3px;
}

QSplitter::handle:vertical {
    height: 3px;
}

/* Line edits */
QLineEdit {
    background-color: #4a4a4a;
    border: 1px solid #666666;
    border-radius: 4px;
    padding: 4px;
    color: #ffffff;
}

QLineEdit:hover {
    border-color: #777777;
}

QLineEdit:focus {
    border-color: #4a90e2;
}

/* Check boxes */
QCheckBox {
    color: #ffffff;
    spacing: 5px;
}

QCheckBox::indicator {
    width: 13px;
    height: 13px;
}

QCheckBox::indicator:unchecked {
    background-color: #4a4a4a;
    border: 1px solid #666666;
    border-radius: 2px;
}

QCheckBox::indicator:checked {
    background-color: #4a90e2;
    border: 1px solid #4a90e2;
    border-radius: 2px;
}

/* Dialog styling */
QDialog {
    background-color: #2b2b2b;
    color: #ffffff;
}

/* Message box styling */
QMessageBox {
    background-color: #2b2b2b;
    color: #ffffff;
}

QMessageBox QPushButton {
    min-width: 80px;
}
